# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

MagicBox is a native iOS application built with Swift and UIKit that provides a template-based item management system. The app allows users to create custom templates with various component types and manage items based on those templates.

## Technology Stack

- **Language**: Swift 5.9
- **UI Framework**: UIKit (primary) + SwiftUI (for charts)
- **Database**: SQLite.swift
- **Layout**: SnapKit for Auto Layout
- **Minimum iOS Version**: 16.0
- **Supported Devices**: iPhone and iPad

## Core Architecture

### Data Model Hierarchy
```
Template → Component → Option → Item → Value
```

1. **Templates** define the structure (e.g., "Book Template")
2. **Components** are fields within templates (e.g., "Title", "Author", "Rating")
3. **Options** are possible values for components (e.g., rating options: 1-5 stars)
4. **Items** are instances created from templates
5. **Values** store the actual data for each item's components

### Key Database Tables
- `templates`: Template definitions
- `components`: Component definitions with types (0=text, 1=number, 2=select, 3=multiSelect, 4=color)
- `component_options`: Predefined options for select/multiSelect components
- `items`: Individual items created from templates
- `item_values`: Actual values for each item's components

## Project Structure

- `magicBox/`: Main application directory
  - `Core/`: Core functionality (Logger, Database, Extensions)
  - `Models/`: Data models matching database schema
  - `ViewControllers/`: Tab-based view controllers
    - `HomeViewController`: Main dashboard
    - `DataViewController`: Template/component management
    - `StatisticsViewController`: Charts and analytics
    - `MineViewController`: User settings
  - `Views/`: Custom UI components
  - `Resources/`: Assets, localization files, Info.plist

## Development Commands

```bash
# Open project in Xcode
open magicBox.xcodeproj

# Build project
xcodebuild -project magicBox.xcodeproj -scheme magicBox -configuration Debug

# Run on simulator
xcodebuild -project magicBox.xcodeproj -scheme magicBox -destination 'platform=iOS Simulator,name=iPhone 15' -configuration Debug

# Clean build folder
xcodebuild -project magicBox.xcodeproj -scheme magicBox -configuration Debug clean
```

## Key Implementation Details

### Logger System
The app uses a custom Logger class with business-specific prefixes:
```swift
logger.debug("message")          // General debug
logger.biz.template("message")   // Template-related logs
logger.biz.component("message")  // Component-related logs
logger.biz.item("message")       // Item-related logs
```

### Database Access Pattern
All database operations go through `DatabaseManager.shared`:
```swift
let templates = DatabaseManager.shared.fetchTemplates()
DatabaseManager.shared.insertTemplate(template)
```

### Localization
The app supports multiple languages using `Localizable.strings`:
- English (Base)
- Simplified Chinese (zh-Hans)
- Traditional Chinese (zh-Hant)

Access localized strings: `NSLocalizedString("key", comment: "")`

## Common Development Tasks

### Adding a New Component Type
1. Add the type to `ComponentType` enum in Component.swift
2. Update UI in `AddComponentViewController` to handle the new type
3. Implement value handling in `DatabaseManager`
4. Update item detail views to display the new component type

### Working with Templates
- Template creation UI is in `AddTemplateViewController` (currently TODO)
- Template listing is in `DataViewController`
- Database operations are in `DatabaseManager.fetchTemplates()` and related methods

### Debugging Database Issues
1. Check logs with `logger.biz.database` prefix
2. Database file location: App's Documents directory
3. Use `DatabaseManager.shared.printDatabasePath()` to find the exact location

## Development Best Practices

- 每次改完代码以后编译项目，保证不报错
- 编译项目使用16pro的模拟器
- 如果集成了snapkit，那么写布局的时候使用snapkit

## Important Notes

- The app uses SQLite.swift, not Core Data
- Many third-party libraries are included but not fully utilized yet
- Navigation is tab-based with custom `TabBarController`
- The app supports both portrait and landscape orientations
- Dark mode support is implemented through UIKit's trait collection