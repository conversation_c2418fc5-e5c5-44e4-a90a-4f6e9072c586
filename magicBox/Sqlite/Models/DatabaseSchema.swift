import SQLite
import Foundation

// MARK: - 数据库表定义
/// 定义应用程序中使用的所有数据库表
struct Tables {
    // 表名常量
    private static let templatesTableName = "templates"
    private static let templateComponentsTableName = "template_components"
    private static let componentOptionsTableName = "component_options"
    private static let itemsTableName = "items"
    private static let itemAttributeValuesTableName = "item_attribute_values"
    
    /// 模板表 - 存储各种模板的基本信息
    static let templates = Table(templatesTableName)
    /// 模板组件表 - 存储模板中的各种组件配置
    static let templateComponents = Table(templateComponentsTableName)
    /// 组件选项表 - 存储组件的预定义选项
    static let componentOptions = Table(componentOptionsTableName)
    /// 物品表 - 存储基于模板创建的物品实例
    static let items = Table(itemsTableName)
    /// 物品属性值表 - 存储物品的具体属性值
    static let itemAttributeValues = Table(itemAttributeValuesTableName)
    
    /// 获取表名
    static var templatesName: String { templatesTableName }
    static var templateComponentsName: String { templateComponentsTableName }
    static var componentOptionsName: String { componentOptionsTableName }
    static var itemsName: String { itemsTableName }
    static var itemAttributeValuesName: String { itemAttributeValuesTableName }
    
    /// 所有表名的有序列表（按外键依赖关系排序，子表在前）
    static let allTablesInDeletionOrder: [String] = [
        itemAttributeValuesTableName,    // 1. 最底层的子表
        itemsTableName,                  // 2. 物品表
        componentOptionsTableName,       // 3. 组件选项表
        templateComponentsTableName,     // 4. 模板组件表
        templatesTableName              // 5. 最顶层的父表
    ]
    
    /// 检查是否为有效的表名
    /// - Parameter name: 表名
    /// - Returns: 是否为有效表名
    static func isValidTableName(_ name: String) -> Bool {
        return allTablesInDeletionOrder.contains(name.lowercased())
    }
}

// MARK: - 模板表字段定义
/// 定义模板表的所有字段
struct TemplateColumns {
    /// 模板的唯一标识符（主键）
    static let id = SQLite.Expression<Int64>("id")
    /// 模板名称
    static let name = SQLite.Expression<String>("name")
    /// 模板描述（可选）
    static let description = SQLite.Expression<String?>("description")
    /// 模板创建时间
    static let createdAt = SQLite.Expression<Date>("created_at")
    /// 模板最后更新时间
    static let updatedAt = SQLite.Expression<Date>("updated_at")
}

// MARK: - 模板组件表字段定义
/// 定义模板组件表的所有字段
struct TemplateComponentColumns {
    /// 组件的唯一标识符（主键）
    static let id = SQLite.Expression<Int64>("id")
    /// 所属模板的ID（外键）
    static let templateId = SQLite.Expression<Int64>("template_id")
    /// 组件名称（用于程序内部识别）
    static let componentName = SQLite.Expression<String>("component_name")
    /// 组件标题（用于用户界面显示）
    static let componentTitle = SQLite.Expression<String>("component_title")
    /// 组件类型（如：string、number、date、color等）
    static let componentType = SQLite.Expression<String>("component_type")
    /// 是否为必填项
    static let isRequired = SQLite.Expression<Bool>("is_required")
    /// 是否支持多值（可以选择多个选项）
    static let isMultiValue = SQLite.Expression<Bool>("is_multi_value")
    /// 是否有预定义选项
    static let hasPredefinedOptions = SQLite.Expression<Bool>("has_predefined_options")
    /// 是否允许自定义值（在有预定义选项时）
    static let allowCustomValue = SQLite.Expression<Bool>("allow_custom_value")
    /// 组件在模板中的排序顺序
    static let sortOrder = SQLite.Expression<Int>("sort_order")
    /// 组件的默认值（可选）
    static let defaultValue = SQLite.Expression<String?>("default_value")
    /// 是否为全局组件引用
    static let isGlobalComponent = SQLite.Expression<Bool>("is_global_component")
    /// 全局组件类型（当 isGlobalComponent 为 true 时使用）
    static let globalComponentType = SQLite.Expression<String?>("global_component_type")
}

// MARK: - 组件选项表字段定义
/// 定义组件选项表的所有字段
struct ComponentOptionColumns {
    /// 选项的唯一标识符（主键）
    static let id = SQLite.Expression<Int64>("id")
    /// 所属组件的ID（外键）
    static let componentId = SQLite.Expression<Int64>("component_id")
    /// 选项的实际值（存储在数据库中的值）
    static let optionValue = SQLite.Expression<String>("option_value")
    /// 选项的显示名称（用户界面中显示的文本）
    static let optionDisplay = SQLite.Expression<String>("option_display")
    /// 选项的扩展属性（JSON格式，用于存储颜色、图标等额外信息）
    static let metadata = SQLite.Expression<String?>("metadata")
    /// 选项是否处于激活状态
    static let isActive = SQLite.Expression<Bool>("is_active")
    /// 选项的排序顺序
    static let sortOrder = SQLite.Expression<Int>("sort_order")
    /// 选项的创建时间
    static let createdAt = SQLite.Expression<Date>("created_at")
}

// MARK: - 物品表字段定义
/// 定义物品表的所有字段
struct ItemColumns {
    /// 物品的唯一标识符（主键）
    static let id = SQLite.Expression<Int64>("id")
    /// 物品所基于的模板ID（外键）
    static let templateId = SQLite.Expression<Int64>("template_id")
    /// 物品的创建时间
    static let createdAt = SQLite.Expression<Date>("created_at")
    /// 物品的最后更新时间
    static let updatedAt = SQLite.Expression<Date>("updated_at")
}

// MARK: - 物品属性值表字段定义
/// 定义物品属性值表的所有字段
struct ItemAttributeValueColumns {
    /// 属性值的唯一标识符（主键）
    static let id = SQLite.Expression<Int64>("id")
    /// 所属物品的ID（外键）
    static let itemId = SQLite.Expression<Int64>("item_id")
    /// 所属组件的ID（外键）
    static let componentId = SQLite.Expression<Int64>("component_id")
    /// 属性的具体值
    static let value = SQLite.Expression<String>("value")
    /// 属性值的排序顺序（用于多值属性）
    static let sortOrder = SQLite.Expression<Int>("sort_order")
}