import Foundation
import SQLite

// MARK: - 颜色管理器
/// 负责颜色相关的业务逻辑和数据管理
class ColorManager {
    
    // MARK: - 单例
    static let shared = ColorManager()
    
    private init() {}
    
    // MARK: - 常量定义
    /// 颜色组件的ID，使用统一的ComponentType定义
    private var colorComponentId: Int64 {
        return ComponentType.color.componentId
    }
    
    // MARK: - 颜色选项管理
    
    /// 添加自定义颜色
    /// - Parameters:
    ///   - name: 颜色名称
    ///   - value: 颜色值 (hex格式)
    ///   - componentId: 组件ID，默认为颜色组件ID
    /// - Returns: 新创建的ComponentOption的ID
    /// - Throws: 数据库操作错误
    @discardableResult
    func addCustomColor(name: String, value: String, componentId: Int64? = nil) throws -> Int64 {
        let targetComponentId = componentId ?? colorComponentId
        
        // 验证颜色值格式
        guard isValidColorValue(value) else {
            throw ColorManagerError.invalidColorValue(value)
        }
        
        // 检查颜色名称是否已存在
        if try colorOptionExists(name: name, componentId: targetComponentId) {
            throw ColorManagerError.colorNameAlreadyExists(name)
        }
        
        // 获取当前选项的数量，用于设置排序顺序
        let existingOptions = try DatabaseManager.shared.getComponentOptions(componentId: targetComponentId)
        let sortOrder = existingOptions.count
        
        let option = ComponentOption(
            componentId: targetComponentId,
            optionValue: value,
            optionDisplay: name,
            metadata: createCustomColorMetadata(),
            isActive: true,
            sortOrder: sortOrder
        )
        
        let optionId = try DatabaseManager.shared.createComponentOption(option)
        Logger.database.info("成功添加自定义颜色: \(name) (\(value))")
        
        return optionId
    }
    
    /// 更新颜色选项
    /// - Parameters:
    ///   - id: 选项ID
    ///   - name: 新的颜色名称
    ///   - value: 新的颜色值
    /// - Returns: 更新是否成功
    /// - Throws: 数据库操作错误
    @discardableResult
    func updateColor(id: Int64, name: String, value: String) throws -> Bool {
        // 验证颜色值格式
        guard isValidColorValue(value) else {
            throw ColorManagerError.invalidColorValue(value)
        }
        
        // 获取现有选项
        guard let existingOption = try getColorOption(id: id) else {
            throw ColorManagerError.colorNotFound(id)
        }
        
        // 如果名称有变化，检查新名称是否已存在
        if existingOption.optionDisplay != name {
            if try colorOptionExists(name: name, componentId: existingOption.componentId) {
                throw ColorManagerError.colorNameAlreadyExists(name)
            }
        }
        
        let updatedOption = ComponentOption(
            id: id,
            componentId: existingOption.componentId,
            optionValue: value,
            optionDisplay: name,
            metadata: existingOption.metadata,
            isActive: existingOption.isActive,
            sortOrder: existingOption.sortOrder,
            createdAt: existingOption.createdAt
        )
        
        let success = try DatabaseManager.shared.updateComponentOption(id: id, option: updatedOption)
        if success {
            Logger.database.info("成功更新颜色: \(name) (\(value))")
        }
        
        return success
    }
    
    /// 删除颜色选项
    /// - Parameter id: 选项ID
    /// - Returns: 删除是否成功
    /// - Throws: 数据库操作错误
    @discardableResult
    func deleteColor(id: Int64) throws -> Bool {
        let success = try DatabaseManager.shared.deleteComponentOption(id: id)
        if success {
            Logger.database.info("成功删除颜色选项，ID: \(id)")
        }
        return success
    }
    
    /// 切换颜色选项的激活状态
    /// - Parameters:
    ///   - id: 选项ID
    ///   - isActive: 是否激活
    /// - Returns: 更新是否成功
    /// - Throws: 数据库操作错误
    @discardableResult
    func toggleColorActive(id: Int64, isActive: Bool) throws -> Bool {
        let success = try DatabaseManager.shared.toggleComponentOption(id: id, isActive: isActive)
        if success {
            Logger.database.info("成功切换颜色选项状态，ID: \(id), 激活: \(isActive)")
        }
        return success
    }
    
    // MARK: - 查询方法
    
    /// 获取所有颜色选项
    /// - Parameter componentId: 组件ID，默认为颜色组件ID
    /// - Returns: 颜色选项数组
    /// - Throws: 数据库操作错误
    func getAllColors(componentId: Int64? = nil) throws -> [ComponentOption] {
        let targetComponentId = componentId ?? colorComponentId
        return try DatabaseManager.shared.getComponentOptions(componentId: targetComponentId)
    }
    
    /// 获取所有颜色选项（包括禁用的）
    /// - Parameter componentId: 组件ID，默认为颜色组件ID
    /// - Returns: 颜色选项数组
    /// - Throws: 数据库操作错误
    func getAllColorsIncludingInactive(componentId: Int64? = nil) throws -> [ComponentOption] {
        let targetComponentId = componentId ?? colorComponentId
        return try DatabaseManager.shared.getAllComponentOptions(componentId: targetComponentId)
    }
    
    /// 根据ID获取颜色选项
    /// - Parameter id: 选项ID
    /// - Returns: 颜色选项，如果不存在则返回nil
    /// - Throws: 数据库操作错误
    func getColorOption(id: Int64) throws -> ComponentOption? {
        let allOptions = try getAllColorsIncludingInactive()
        return allOptions.first { $0.id == id }
    }
    
    /// 检查颜色选项是否存在
    /// - Parameters:
    ///   - name: 颜色名称
    ///   - componentId: 组件ID
    /// - Returns: 是否存在
    /// - Throws: 数据库操作错误
    func colorOptionExists(name: String, componentId: Int64? = nil) throws -> Bool {
        let targetComponentId = componentId ?? colorComponentId
        let options = try DatabaseManager.shared.getComponentOptions(componentId: targetComponentId)
        return options.contains { $0.optionDisplay == name }
    }
    
    /// 获取颜色选项数量
    /// - Parameter componentId: 组件ID
    /// - Returns: 颜色选项数量
    /// - Throws: 数据库操作错误
    func getColorOptionsCount(componentId: Int64? = nil) throws -> Int {
        let targetComponentId = componentId ?? colorComponentId
        return try DatabaseManager.shared.getComponentOptions(componentId: targetComponentId).count
    }
    
    /// 根据颜色值查找颜色选项
    /// - Parameters:
    ///   - value: 颜色值
    ///   - componentId: 组件ID
    /// - Returns: 匹配的颜色选项数组
    /// - Throws: 数据库操作错误
    func findColorsByValue(_ value: String, componentId: Int64? = nil) throws -> [ComponentOption] {
        let targetComponentId = componentId ?? colorComponentId
        let options = try DatabaseManager.shared.getComponentOptions(componentId: targetComponentId)
        return options.filter { $0.optionValue.lowercased() == value.lowercased() }
    }
    
    // MARK: - 验证方法
    
    /// 验证颜色值格式是否正确
    /// - Parameter colorValue: 颜色值
    /// - Returns: 是否为有效的颜色值格式
    func isValidColorValue(_ colorValue: String) -> Bool {
        let hexPattern = "^#[0-9A-Fa-f]{6}$"
        let regex = try? NSRegularExpression(pattern: hexPattern, options: [])
        let range = NSRange(location: 0, length: colorValue.count)
        return regex?.firstMatch(in: colorValue, options: [], range: range) != nil
    }
    
    /// 验证颜色名称是否有效
    /// - Parameter name: 颜色名称
    /// - Returns: 是否有效
    func isValidColorName(_ name: String) -> Bool {
        let trimmedName = name.trimmingCharacters(in: .whitespacesAndNewlines)
        return !trimmedName.isEmpty && trimmedName.count <= 50
    }
    
    // MARK: - 工具方法
    /// 创建自定义颜色的元数据
    /// - Returns: JSON格式的元数据字符串
    private func createCustomColorMetadata() -> String? {
        let metadata = [
            "type": "custom",
            "category": "color",
            "created_by": "user"
        ]
        
        guard let jsonData = try? JSONSerialization.data(withJSONObject: metadata, options: []),
              let jsonString = String(data: jsonData, encoding: .utf8) else {
            return nil
        }
        
        return jsonString
    }
}

// MARK: - 错误定义
enum ColorManagerError: Error, LocalizedError {
    case invalidColorValue(String)
    case colorNameAlreadyExists(String)
    case colorNotFound(Int64)
    case invalidColorName(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidColorValue(let value):
            return "无效的颜色值: \(value)，请使用 #RRGGBB 格式"
        case .colorNameAlreadyExists(let name):
            return "颜色名称 '\(name)' 已存在"
        case .colorNotFound(let id):
            return "未找到ID为 \(id) 的颜色选项"
        case .invalidColorName(let name):
            return "无效的颜色名称: \(name)"
        }
    }
}

// MARK: - 扩展方法
extension ColorManager {
    
    /// 批量添加颜色
    /// - Parameter colors: 颜色数组 (name, value)
    /// - Returns: 成功添加的数量
    /// - Throws: 数据库操作错误
    @discardableResult
    func batchAddColors(_ colors: [(name: String, value: String)]) throws -> Int {
        var successCount = 0
        
        // 使用事务批量添加颜色，提高性能并确保原子性
        try DatabaseManager.shared.performTransaction { [self] db in
            // 获取当前最大的排序值
            let maxSortOrder = try db.scalar(Tables.componentOptions
                .filter(ComponentOptionColumns.componentId == colorComponentId)
                .select(ComponentOptionColumns.sortOrder.max)) ?? -1
            
            for (index, color) in colors.enumerated() {
                // 验证颜色格式
                guard isValidColorValue(color.value) && isValidColorName(color.name) else {
                    Logger.database.info("跳过无效颜色: \(color.name) - \(color.value)")
                    continue
                }
                
                // 检查是否已存在
                let existingCount = try db.scalar(Tables.componentOptions
                    .filter(ComponentOptionColumns.componentId == colorComponentId)
                    .filter(ComponentOptionColumns.optionValue == color.value)
                    .count)
                
                if existingCount > 0 {
                    Logger.database.info("颜色已存在，跳过: \(color.name)")
                    continue
                }
                
                // 插入新颜色
                let _ = try db.run(Tables.componentOptions.insert(
                    ComponentOptionColumns.componentId <- colorComponentId,
                    ComponentOptionColumns.optionValue <- color.value,
                    ComponentOptionColumns.optionDisplay <- color.name,
                    ComponentOptionColumns.metadata <- nil,
                    ComponentOptionColumns.isActive <- true,
                    ComponentOptionColumns.sortOrder <- maxSortOrder + index + 1
                ))
                
                successCount += 1
            }
        }
        
        Logger.database.info("批量添加颜色完成，成功: \(successCount)/\(colors.count)")
        return successCount
    }
    
    /// 清理无效的颜色选项
    /// - Returns: 清理的数量
    /// - Throws: 数据库操作错误
    @discardableResult
    func cleanupInvalidColors() throws -> Int {
        var cleanedCount = 0
        var invalidColorIds: [Int64] = []
        
        // 先收集所有无效颜色的ID
        let allColors = try getAllColorsIncludingInactive()
        for color in allColors {
            if !isValidColorValue(color.optionValue) || !isValidColorName(color.optionDisplay) {
                if let colorId = color.id {
                    invalidColorIds.append(colorId)
                }
            }
        }
        
        // 如果有无效颜色，使用事务批量删除
        if !invalidColorIds.isEmpty {
            try DatabaseManager.shared.performTransaction { db in
                for colorId in invalidColorIds {
                    let deletedRows = try db.run(Tables.componentOptions
                        .filter(ComponentOptionColumns.id == colorId)
                        .delete())
                    
                    if deletedRows > 0 {
                        cleanedCount += 1
                    }
                }
            }
            
            Logger.database.info("清理无效颜色选项完成，清理数量: \(cleanedCount)")
        }
        
        return cleanedCount
    }
} 
