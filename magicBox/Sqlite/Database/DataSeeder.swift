//
//  DataSeeder.swift
//  magicBox
//
//  Created by xmly on 2025/7/8.
//

import Foundation
import SQLite

/// 数据预设管理器
/// 专门负责向数据库添加预设数据
class DataSeeder {
    
    // MARK: - 单例
    static let shared = DataSeeder()
    private init() {}
    
    // MARK: - 公开方法
    
    /// 初始化预设数据
    /// - Throws: 数据库操作错误
    func initializeDefaultData() throws {
        Logger.database.info("🚀 开始初始化预设数据...")
        
        // 定义需要初始化的组件类型和对应的预设数据
        let componentInitData: [(type: ComponentType, data: [(key: String, display: String, value: String)])] = [
            (.color, PresetData.predefinedColors.map { ($0.key, $0.name, $0.value) }),
            (.brand, PresetData.predefinedBrands),
            (.size, PresetData.predefinedSizes),
            (.material, PresetData.predefinedMaterials),
            (.condition, PresetData.predefinedConditions),
            (.season, PresetData.predefinedSeasons),
            (.category, PresetData.predefinedCategories)
        ]
        
        Logger.database.info("📊 准备初始化 \(componentInitData.count) 种组件类型")
        
        // 检查每个组件类型是否需要初始化
        var needsInit = false
        var initTasks: [(ComponentType, [(key: String, display: String, value: String)])] = []
        
        for (type, data) in componentInitData {
            let existingOptions = try DatabaseManager.shared.getComponentOptions(componentId: type.componentId)
            if existingOptions.isEmpty {
                needsInit = true
                initTasks.append((type, data))
                Logger.database.info("组件 \(type.name) 需要初始化预设数据")
            }
        }
        
        // 如果有任何数据需要初始化，使用一个事务完成所有操作
        if needsInit {
            Logger.database.info("🚀 开始插入预设数据，共 \(initTasks.count) 种组件类型需要初始化")
            
            try DatabaseManager.shared.performTransaction { db in
                for (type, data) in initTasks {
                    var count = 0
                    Logger.database.info("📝 正在初始化 \(type.name) 组件，共 \(data.count) 条数据...")
                    
                    for (index, item) in data.enumerated() {
                        let option = ComponentOption(
                            componentId: type.componentId,
                            optionValue: item.value,
                            optionDisplay: item.display,
                            metadata: PresetData.createMetadata(key: item.key, category: type.rawValue),
                            isActive: true,
                            sortOrder: index
                        )
                        
                        do {
                            let insertId = try db.run(Tables.componentOptions.insert(
                                ComponentOptionColumns.componentId <- option.componentId,
                                ComponentOptionColumns.optionValue <- option.optionValue,
                                ComponentOptionColumns.optionDisplay <- option.optionDisplay,
                                ComponentOptionColumns.metadata <- option.metadata,
                                ComponentOptionColumns.isActive <- option.isActive,
                                ComponentOptionColumns.sortOrder <- option.sortOrder
                            ))
                            
                            Logger.database.debug("  ✅ 插入: \(option.optionDisplay) (ID: \(insertId))")
                            count += 1
                        } catch {
                            Logger.database.error("  ❌ 插入失败: \(option.optionDisplay) - \(error)")
                            throw error
                        }
                    }
                    
                    Logger.database.info("✅ 成功初始化 \(type.name) 预设数据，共添加 \(count) 个选项")
                }
            }
            
            Logger.database.info("🎉 所有预设数据初始化完成！")
        } else {
            Logger.database.info("⚠️ 所有组件都已有数据，跳过预设数据初始化")
        }
    }
}
