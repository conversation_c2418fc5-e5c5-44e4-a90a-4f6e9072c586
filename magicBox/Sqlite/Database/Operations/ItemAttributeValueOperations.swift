import SQLite
import Foundation

// MARK: - ItemAttributeValue 相关操作
extension DatabaseManager {
    
    // MARK: - 保存物品属性值
    func saveItemAttributeValue(_ value: ItemAttributeValue) throws -> Int64 {
        return try safeExecute { db in
            let insert = Tables.itemAttributeValues.insert(
                ItemAttributeValueColumns.itemId <- value.itemId,
                ItemAttributeValueColumns.componentId <- value.componentId,
                ItemAttributeValueColumns.value <- value.value,
                ItemAttributeValueColumns.sortOrder <- value.sortOrder
            )
            return try db.run(insert)
        }
    }
    
    // MARK: - 保存物品属性值（批量，支持多值）
    func saveItemAttributeValues(itemId: Int64, componentId: Int64, values: [String]) throws {
        // 使用事务确保原子性：删除旧值并插入新值
        try performTransaction { db in
            // 先删除旧值
            let deleteQuery = Tables.itemAttributeValues.filter(ItemAttributeValueColumns.itemId == itemId && ItemAttributeValueColumns.componentId == componentId)
            try db.run(deleteQuery.delete())
            
            // 批量插入新值
            for (index, value) in values.enumerated() {
                let _ = try db.run(Tables.itemAttributeValues.insert(
                    ItemAttributeValueColumns.itemId <- itemId,
                    ItemAttributeValueColumns.componentId <- componentId,
                    ItemAttributeValueColumns.value <- value,
                    ItemAttributeValueColumns.sortOrder <- index + 1
                ))
            }
            
            // 更新物品的修改时间
            let now = Date()
            try db.run(Tables.items.filter(ItemColumns.id == itemId).update(ItemColumns.updatedAt <- now))
        }
    }
    
    // MARK: - 获取物品的所有属性值
    func getItemAttributeValues(itemId: Int64) throws -> [ItemAttributeValue] {
        return try safeExecute { db in
            var result: [ItemAttributeValue] = []
            let query = Tables.itemAttributeValues.filter(ItemAttributeValueColumns.itemId == itemId).order(ItemAttributeValueColumns.componentId, ItemAttributeValueColumns.sortOrder)
            for row in try db.prepare(query) {
                result.append(ItemAttributeValue(
                    id: row[ItemAttributeValueColumns.id],
                    itemId: row[ItemAttributeValueColumns.itemId],
                    componentId: row[ItemAttributeValueColumns.componentId],
                    value: row[ItemAttributeValueColumns.value],
                    sortOrder: row[ItemAttributeValueColumns.sortOrder]
                ))
            }
            return result
        }
    }
    
    // MARK: - 获取物品指定组件的属性值
    func getItemAttributeValues(itemId: Int64, componentId: Int64) throws -> [ItemAttributeValue] {
        return try safeExecute { db in
            var result: [ItemAttributeValue] = []
            let query = Tables.itemAttributeValues.filter(ItemAttributeValueColumns.itemId == itemId && ItemAttributeValueColumns.componentId == componentId).order(ItemAttributeValueColumns.sortOrder)
            for row in try db.prepare(query) {
                result.append(ItemAttributeValue(
                    id: row[ItemAttributeValueColumns.id],
                    itemId: row[ItemAttributeValueColumns.itemId],
                    componentId: row[ItemAttributeValueColumns.componentId],
                    value: row[ItemAttributeValueColumns.value],
                    sortOrder: row[ItemAttributeValueColumns.sortOrder]
                ))
            }
            return result
        }
    }
    
    // MARK: - 删除物品的指定属性值
    func deleteItemAttributeValue(id: Int64) throws -> Bool {
        return try safeExecute { db in
            let value = Tables.itemAttributeValues.filter(ItemAttributeValueColumns.id == id)
            return try db.run(value.delete()) > 0
        }
    }
    
    // MARK: - 删除物品的指定组件的所有属性值
    func deleteItemAttributeValues(itemId: Int64, componentId: Int64) throws -> Bool {
        return try safeExecute { db in
            let values = Tables.itemAttributeValues.filter(ItemAttributeValueColumns.itemId == itemId && ItemAttributeValueColumns.componentId == componentId)
            return try db.run(values.delete()) > 0
        }
    }
} 