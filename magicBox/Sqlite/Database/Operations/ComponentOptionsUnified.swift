import Foundation
import SQLite

// MARK: - 统一的组件选项获取方法
extension DatabaseManager {
    
    /// 获取组件的选项列表（统一处理全局组件和模板组件）
    /// - Parameter component: 模板组件
    /// - Returns: 组件选项数组
    func getOptionsForComponent(_ component: TemplateComponent) throws -> [ComponentOption] {
        if component.isGlobalComponent, let globalType = component.globalComponentTypeEnum {
            // 全局组件：使用 ComponentType 的固定 ID
            return try getComponentOptions(componentId: globalType.componentId)
        } else {
            // 模板特定组件：使用组件自身的 ID
            guard let componentId = component.id else {
                throw DatabaseError.operationFailed("Component ID is nil")
            }
            return try getComponentOptions(componentId: componentId)
        }
    }
    
    /// 根据物品属性值获取对应的选项显示信息
    /// - Parameters:
    ///   - attributeValue: 物品属性值
    ///   - component: 对应的模板组件
    /// - Returns: 匹配的组件选项，如果找不到返回 nil
    func getOptionForAttributeValue(_ attributeValue: ItemAttributeValue, component: TemplateComponent) throws -> ComponentOption? {
        let options = try getOptionsForComponent(component)
        return options.first { $0.optionValue == attributeValue.value }
    }
    
    /// 批量获取组件选项（优化查询）
    /// - Parameter components: 模板组件数组
    /// - Returns: 组件ID到选项数组的映射
    func batchGetOptionsForComponents(_ components: [TemplateComponent]) throws -> [Int64: [ComponentOption]] {
        var result: [Int64: [ComponentOption]] = [:]
        
        // 分离全局组件和模板组件
        let globalComponents = components.filter { $0.isGlobalComponent }
        let templateComponents = components.filter { !$0.isGlobalComponent }
        
        // 获取全局组件选项
        for component in globalComponents {
            if let globalType = component.globalComponentTypeEnum {
                let options = try getComponentOptions(componentId: globalType.componentId)
                if let componentId = component.id {
                    result[componentId] = options
                }
            }
        }
        
        // 获取模板组件选项
        let templateComponentIds = templateComponents.compactMap { $0.id }
        if !templateComponentIds.isEmpty {
            let allOptions = try batchGetComponentOptions(componentIds: templateComponentIds)
            for (componentId, options) in allOptions {
                result[componentId] = options
            }
        }
        
        return result
    }
    
    /// 批量获取指定组件ID的选项
    private func batchGetComponentOptions(componentIds: [Int64]) throws -> [Int64: [ComponentOption]] {
        return try safeExecute { db in
            var result: [Int64: [ComponentOption]] = [:]
            
            let query = Tables.componentOptions
                .filter(componentIds.contains(ComponentOptionColumns.componentId))
                .order(ComponentOptionColumns.componentId.asc, ComponentOptionColumns.sortOrder.asc)
            
            for row in try db.prepare(query) {
                let option = ComponentOption(
                    id: row[ComponentOptionColumns.id],
                    componentId: row[ComponentOptionColumns.componentId],
                    optionValue: row[ComponentOptionColumns.optionValue],
                    optionDisplay: row[ComponentOptionColumns.optionDisplay],
                    metadata: row[ComponentOptionColumns.metadata],
                    isActive: row[ComponentOptionColumns.isActive],
                    sortOrder: row[ComponentOptionColumns.sortOrder],
                    createdAt: row[ComponentOptionColumns.createdAt]
                )
                
                let componentId = row[ComponentOptionColumns.componentId]
                if result[componentId] == nil {
                    result[componentId] = []
                }
                result[componentId]?.append(option)
            }
            
            return result
        }
    }
}

// MARK: - 辅助方法
extension DatabaseManager {
    
    /// 检查组件是否应该使用全局选项
    /// - Parameter component: 模板组件
    /// - Returns: 是否使用全局选项
    func shouldUseGlobalOptions(for component: TemplateComponent) -> Bool {
        return component.isGlobalComponent && component.globalComponentType != nil
    }
    
    /// 获取组件的有效组件ID（考虑全局组件的情况）
    /// - Parameter component: 模板组件
    /// - Returns: 用于查询选项的组件ID
    func getEffectiveComponentId(for component: TemplateComponent) throws -> Int64 {
        if component.isGlobalComponent, let globalType = component.globalComponentTypeEnum {
            return globalType.componentId
        } else {
            guard let componentId = component.id else {
                throw DatabaseError.operationFailed("Component ID is nil")
            }
            return componentId
        }
    }
}