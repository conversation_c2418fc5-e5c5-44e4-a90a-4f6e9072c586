import SQLite
import Foundation

// MARK: - 数据库错误类型
enum DatabaseError: Error {
    case connectionFailed
    case operationFailed(String)
    case tableNotFound
    case invalidData
}

// MARK: - 数据库管理器
class DatabaseManager {
    internal var db: Connection?
    private let dbPath = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first!
    
    // 连接状态
    var isConnected: Bool {
        return db != nil
    }
    
    // 单例
    static let shared = DatabaseManager()
    
    private init() {
        do {
            let path = "\(dbPath)/magicbox.sqlite3"
            db = try Connection(path)
            print("Database path: \(path)")
            
            // 启用 SQL 日志（调试用）
            #if DEBUG
            db?.trace { sql in
                Logger.database.debug("🔍 SQL: \(sql)")
            }
            Logger.database.info("📝 SQL 日志已启用（DEBUG模式）")
            #endif
            
            // 启用外键支持
            try db?.execute("PRAGMA foreign_keys = ON")
            print("Foreign key support enabled")
            
            // 创建表（如果不存在）
            createTables()
            
            // 执行数据库迁移（添加新字段）
            performMigrations()
        } catch {
            print("Database connection failed: \(error)")
            db = nil
        }
    }
    
    // MARK: - 获取数据库连接
    func getConnection() -> Connection? {
        return db
    }
    
    // MARK: - 安全执行数据库操作
    internal func safeExecute<T>(_ operation: (Connection) throws -> T) throws -> T {
        guard let db = db else {
            throw DatabaseError.connectionFailed
        }
        return try operation(db)
    }
    
    // MARK: - 事务支持
    /// 在事务中执行操作，确保原子性
    /// - Parameter operations: 需要在事务中执行的操作
    /// - Returns: 操作结果
    /// - Throws: 如果任何操作失败，整个事务将回滚
    func performTransaction<T>(_ operations: @escaping (Connection) throws -> T) throws -> T {
        return try safeExecute { db in
            var result: T!
            try db.transaction {
                result = try operations(db)
            }
            return result
        }
    }
    
    // MARK: - 批量操作支持
    /// 批量执行多个操作，使用事务确保原子性
    /// - Parameter operations: 需要批量执行的操作数组
    /// - Throws: 如果任何操作失败，所有操作都会回滚
    func performBatchOperations(_ operations: [(Connection) throws -> Void]) throws {
        try performTransaction { db in
            for operation in operations {
                try operation(db)
            }
        }
    }
    
    // MARK: - 创建所有表
    private func createTables() {
        guard let _ = db else { return }
        
        do {
            try createTemplatesTable()
            try createTemplateComponentsTable()
            try createComponentOptionsTable()
            try createItemsTable()
            try createItemAttributeValuesTable()
            print("All tables created successfully")
        } catch {
            print("Create tables failed: \(error)")
        }
    }
    
    // MARK: - 数据库重置
    func resetDatabase() throws {
        try safeExecute { db in
            try db.run(Tables.itemAttributeValues.drop(ifExists: true))
            try db.run(Tables.items.drop(ifExists: true))
            try db.run(Tables.componentOptions.drop(ifExists: true))
            try db.run(Tables.templateComponents.drop(ifExists: true))
            try db.run(Tables.templates.drop(ifExists: true))
            createTables()
            print("Database reset successfully")
        }
    }
    
    // MARK: - 清空所有表数据
    /// 删除所有表中的记录，但保留表结构
    func clearAllData() throws {
        print("Starting to clear all table data...")
        
        // 使用事务确保所有表的清空操作要么全部成功，要么全部失败
        try performTransaction { db in
            var totalDeleted = 0
            
            // 使用预定义的删除顺序清空每个表
            for tableName in Tables.allTablesInDeletionOrder {
                var deletedCount: Int = 0
                
                switch tableName.lowercased() {
                case Tables.itemAttributeValuesName:
                    deletedCount = try db.run(Tables.itemAttributeValues.delete())
                case Tables.itemsName:
                    deletedCount = try db.run(Tables.items.delete())
                case Tables.componentOptionsName:
                    deletedCount = try db.run(Tables.componentOptions.delete())
                case Tables.templateComponentsName:
                    deletedCount = try db.run(Tables.templateComponents.delete())
                case Tables.templatesName:
                    deletedCount = try db.run(Tables.templates.delete())
                default:
                    print("Unknown table: \(tableName)")
                }
                
                totalDeleted += deletedCount
                print("Deleted \(deletedCount) records from \(tableName)")
            }
            
            print("All table data cleared successfully. Total records deleted: \(totalDeleted)")
        }
    }
    
    // MARK: - 清空指定表数据
    /// 删除指定表中的所有记录
    /// - Parameter tableName: 表名
    func clearTableData(_ tableName: String) throws {
        // 首先检查表名是否有效
        guard Tables.isValidTableName(tableName) else {
            throw DatabaseError.tableNotFound
        }
        
        try safeExecute { db in
            var deletedCount: Int = 0
            
            switch tableName.lowercased() {
            case Tables.templatesName:
                deletedCount = try db.run(Tables.templates.delete())
            case Tables.templateComponentsName:
                deletedCount = try db.run(Tables.templateComponents.delete())
            case Tables.componentOptionsName:
                deletedCount = try db.run(Tables.componentOptions.delete())
            case Tables.itemsName:
                deletedCount = try db.run(Tables.items.delete())
            case Tables.itemAttributeValuesName:
                deletedCount = try db.run(Tables.itemAttributeValues.delete())
            default:
                throw DatabaseError.tableNotFound
            }
            
            print("Deleted \(deletedCount) records from \(tableName)")
        }
    }
    
    // MARK: - 检查表是否存在
    private func tableExists(_ tableName: String) -> Bool {
        guard let db = db else { return false }
        
        do {
            let count = try db.scalar("SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name=?", tableName) as? Int64
            return count ?? 0 > 0
        } catch {
            return false
        }
    }
    
    // MARK: - 数据库信息
    func getDatabaseInfo() -> [String: Any] {
        var info: [String: Any] = [:]
        
        guard let db = db else { 
            info["error"] = "Database connection failed"
            return info
        }
        
        do {
            info["path"] = dbPath + "/magicbox.sqlite3"
            info["version"] = try db.scalar("SELECT sqlite_version()") as? String ?? "Unknown"
            
            // 检查外键支持状态
            info["foreign_keys_enabled"] = try db.scalar("PRAGMA foreign_keys") as? Int64 == 1
            
            // 统计各表的记录数
            if tableExists(Tables.templatesName) {
                info["templates_count"] = try db.scalar(Tables.templates.count)
            }
            if tableExists(Tables.templateComponentsName) {
                info["template_components_count"] = try db.scalar(Tables.templateComponents.count)
            }
            if tableExists(Tables.componentOptionsName) {
                info["component_options_count"] = try db.scalar(Tables.componentOptions.count)
            }
            if tableExists(Tables.itemsName) {
                info["items_count"] = try db.scalar(Tables.items.count)
            }
            if tableExists(Tables.itemAttributeValuesName) {
                info["item_attribute_values_count"] = try db.scalar(Tables.itemAttributeValues.count)
            }
        } catch {
            print("Get database info failed: \(error)")
            info["error"] = "Failed to get database info: \(error.localizedDescription)"
        }
        
        return info
    }
    
    // MARK: - 检查外键约束
    func checkForeignKeyConstraints() throws -> Bool {
        return try safeExecute { db in
            let violations = try db.scalar("PRAGMA foreign_key_check") as? [[Any]] ?? []
            if !violations.isEmpty {
                print("发现外键约束违反：\(violations)")
                return false
            }
            return true
        }
    }
}
