//
//  PresetData.swift
//  magicBox
//
//  Created on 2025/7/15.
//

import Foundation

/// 预设数据管理
/// 集中管理所有预设数据，便于维护和扩展
struct PresetData {
    
    // MARK: - 颜色预设数据
    /// 预定义的颜色选项（保留前10个常用颜色）
    static let predefinedColors: [(key: String, name: String, value: String)] = [
        ("black", "黑色", "#000000"),
        ("white", "白色", "#FFFFFF"),
        ("gray", "灰色", "#808080"),
        ("navy", "深蓝", "#000080"),
        ("blue", "蓝色", "#0000FF"),
        ("red", "红色", "#FF0000"),
        ("brown", "棕色", "#A52A2A"),
        ("beige", "米色", "#F5F5DC"),
        ("pink", "粉色", "#FFC0CB"),
        ("green", "绿色", "#00FF00")
    ]
    
    // MARK: - 品牌预设数据
    /// 预定义的品牌选项
    static let predefinedBrands: [(key: String, display: String, value: String)] = [
        ("nike", "Nike", "nike"),
        ("adidas", "Adidas", "adidas"),
        ("uniqlo", "Uniqlo", "uniqlo"),
        ("zara", "ZARA", "zara"),
        ("hm", "H&M", "hm"),
        ("gap", "GAP", "gap"),
        ("levis", "Levi's", "levis"),
        ("puma", "Puma", "puma"),
        ("vans", "Vans", "vans"),
        ("converse", "Converse", "converse")
    ]
    
    // MARK: - 尺码预设数据
    /// 预定义的尺码选项
    static let predefinedSizes: [(key: String, display: String, value: String)] = [
        ("xs", "XS", "xs"),
        ("s", "S", "s"),
        ("m", "M", "m"),
        ("l", "L", "l"),
        ("xl", "XL", "xl"),
        ("xxl", "XXL", "xxl"),
        ("xxxl", "XXXL", "xxxl"),
        ("free", "均码", "free")
    ]
    
    // MARK: - 材质预设数据
    /// 预定义的材质选项
    static let predefinedMaterials: [(key: String, display: String, value: String)] = [
        ("cotton", "纯棉", "cotton"),
        ("polyester", "聚酯纤维", "polyester"),
        ("wool", "羊毛", "wool"),
        ("silk", "丝绸", "silk"),
        ("linen", "亚麻", "linen"),
        ("denim", "牛仔布", "denim"),
        ("leather", "皮革", "leather"),
        ("nylon", "尼龙", "nylon"),
        ("cashmere", "羊绒", "cashmere"),
        ("blend", "混纺", "blend")
    ]
    
    // MARK: - 新旧程度预设数据
    /// 预定义的新旧程度选项
    static let predefinedConditions: [(key: String, display: String, value: String)] = [
        ("new_with_tag", "全新带标", "new_with_tag"),
        ("new_without_tag", "全新无标", "new_without_tag"),
        ("excellent", "九成新", "excellent"),
        ("very_good", "八成新", "very_good"),
        ("good", "七成新", "good"),
        ("fair", "六成新", "fair"),
        ("worn", "已穿用", "worn")
    ]
    
    // MARK: - 季节预设数据
    /// 预定义的季节选项
    static let predefinedSeasons: [(key: String, display: String, value: String)] = [
        ("spring", "春季", "spring"),
        ("summer", "夏季", "summer"),
        ("autumn", "秋季", "autumn"),
        ("winter", "冬季", "winter"),
        ("spring_summer", "春夏", "spring_summer"),
        ("autumn_winter", "秋冬", "autumn_winter"),
        ("all_season", "四季", "all_season")
    ]
    
    // MARK: - 分类预设数据
    /// 预定义的分类选项
    static let predefinedCategories: [(key: String, display: String, value: String)] = [
        ("tshirt", "T恤", "tshirt"),
        ("shirt", "衬衫", "shirt"),
        ("jacket", "外套", "jacket"),
        ("coat", "大衣", "coat"),
        ("sweater", "毛衣", "sweater"),
        ("jeans", "牛仔裤", "jeans"),
        ("pants", "长裤", "pants"),
        ("shorts", "短裤", "shorts"),
        ("dress", "连衣裙", "dress"),
        ("skirt", "半身裙", "skirt")
    ]
    
    // MARK: - 未来可扩展的预设数据
    // 例如：
    // static let predefinedSizes: [(key: String, display: String, value: String)] = []
    // static let predefinedCategories: [(key: String, display: String, value: String)] = []
    // static let predefinedStatuses: [(key: String, display: String, value: String)] = []
    
    // MARK: - 辅助方法
    /// 创建预定义选项的元数据
    /// - Parameters:
    ///   - key: 选项键值
    ///   - category: 选项类别
    /// - Returns: JSON格式的元数据字符串
    static func createMetadata(key: String, category: String) -> String? {
        let metadata = [
            "key": key,
            "type": "predefined",
            "category": category
        ]
        
        guard let data = try? JSONSerialization.data(withJSONObject: metadata) else {
            return nil
        }
        return String(data: data, encoding: .utf8)
    }
}