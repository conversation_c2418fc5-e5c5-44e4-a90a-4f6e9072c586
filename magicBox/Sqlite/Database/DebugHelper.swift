//
//  DebugHelper.swift
//  magicBox
//
//  Created by <PERSON> on 2025/7/15.
//

import Foundation
import SQLite

/// 数据库调试助手
class DebugHelper {
    
    /// 打印 component_options 表中的所有数据
    static func printAllComponentOptions() {
        Logger.database.info("📊 开始打印 component_options 表数据...")
        
        do {
            let db = DatabaseManager.shared.getConnection()
            let query = Tables.componentOptions.select(
                ComponentOptionColumns.id,
                ComponentOptionColumns.componentId,
                ComponentOptionColumns.optionValue,
                ComponentOptionColumns.optionDisplay,
                ComponentOptionColumns.sortOrder
            ).order(ComponentOptionColumns.componentId.asc, ComponentOptionColumns.sortOrder.asc)
            
            guard let db = db else {
                Logger.database.error("❌ 数据库连接为空")
                return
            }
            
            let options = try db.prepare(query)
            
            Logger.database.info("═══════════════════════════════════════════════════")
            Logger.database.info("component_options 表内容:")
            Logger.database.info("═══════════════════════════════════════════════════")
            
            var count = 0
            var currentComponentId: Int64 = -1
            
            for option in options {
                let id = option[ComponentOptionColumns.id]
                let componentId = option[ComponentOptionColumns.componentId]
                let value = option[ComponentOptionColumns.optionValue]
                let display = option[ComponentOptionColumns.optionDisplay]
                let sortOrder = option[ComponentOptionColumns.sortOrder]
                
                if componentId != currentComponentId {
                    currentComponentId = componentId
                    Logger.database.info("───────────────────────────────────────────────────")
                    Logger.database.info("Component ID: \(componentId)")
                    
                    // 尝试识别组件类型
                    let componentName: String
                    switch componentId {
                    case ComponentType.color.componentId:
                        componentName = "颜色 (全局)"
                    case ComponentType.brand.componentId:
                        componentName = "品牌 (全局)"
                    case ComponentType.size.componentId:
                        componentName = "尺码 (全局)"
                    case ComponentType.material.componentId:
                        componentName = "材质 (全局)"
                    case ComponentType.condition.componentId:
                        componentName = "状态 (全局)"
                    case ComponentType.season.componentId:
                        componentName = "季节 (全局)"
                    case ComponentType.category.componentId:
                        componentName = "分类 (全局)"
                    default:
                        componentName = "模板组件"
                    }
                    Logger.database.info("组件类型: \(componentName)")
                    Logger.database.info("───────────────────────────────────────────────────")
                }
                
                Logger.database.info("  ID: \(id), 显示: \(display), 值: \(value), 排序: \(sortOrder)")
                count += 1
            }
            
            Logger.database.info("═══════════════════════════════════════════════════")
            Logger.database.info("总计: \(count) 条记录")
            Logger.database.info("═══════════════════════════════════════════════════")
            
        } catch {
            Logger.database.error("❌ 查询失败: \(error)")
        }
    }
    
    /// 检查特定组件的选项数量
    static func checkComponentOptions(componentId: Int64) {
        do {
            let options = try DatabaseManager.shared.getComponentOptions(componentId: componentId)
            Logger.database.info("📊 Component ID \(componentId) 有 \(options.count) 个选项")
        } catch {
            Logger.database.error("❌ 检查组件选项失败: \(error)")
        }
    }
    
    /// 清理所有全局组件的数据（仅用于调试）
    static func cleanGlobalComponentOptions() {
        #if DEBUG
        Logger.database.warning("⚠️ 清理所有全局组件数据...")
        
        do {
            let componentIds: [Int64] = [
                ComponentType.color.componentId,
                ComponentType.brand.componentId,
                ComponentType.size.componentId,
                ComponentType.material.componentId,
                ComponentType.condition.componentId,
                ComponentType.season.componentId,
                ComponentType.category.componentId
            ]
            
            for componentId in componentIds {
                let deleted = try DatabaseManager.shared.deleteComponentOptions(componentId: componentId)
                if deleted > 0 {
                    Logger.database.info("删除了 component_id=\(componentId) 的 \(deleted) 条记录")
                }
            }
            
            // 清除 UserDefaults 标记
            UserDefaults.standard.removeObject(forKey: "hasInitializedPresetData_v1")
            Logger.database.info("✅ 清理完成，下次启动将重新初始化预设数据")
            
        } catch {
            Logger.database.error("❌ 清理失败: \(error)")
        }
        #endif
    }
}

// MARK: - DatabaseManager 扩展
extension DatabaseManager {
    /// 删除指定组件的所有选项
    func deleteComponentOptions(componentId: Int64) throws -> Int {
        return try safeExecute { db in
            let query = Tables.componentOptions.filter(ComponentOptionColumns.componentId == componentId)
            return try db.run(query.delete())
        }
    }
}