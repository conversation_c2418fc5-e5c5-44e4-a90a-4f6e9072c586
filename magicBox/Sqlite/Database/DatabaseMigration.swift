import Foundation
import SQLite

// MARK: - 数据库迁移
extension DatabaseManager {
    
    /// 执行数据库迁移，添加新字段而不删除数据
    func performMigrations() {
        guard let db = db else { return }
        
        // 迁移1：为 template_components 表添加全局组件支持
        // 其实没用到，因为App第一版已经加字段了，因此注释掉，可以供后续代码参考
        // migrateTemplateComponentsForGlobalSupport(db: db)
        
        Logger.database.info("✅ 数据库迁移完成")
    }
    
    /// 为 template_components 表添加全局组件字段
    private func migrateTemplateComponentsForGlobalSupport(db: Connection) {
        do {
            // 检查字段是否已存在
            let tableInfo = try db.prepare("PRAGMA table_info(template_components)")
            var hasIsGlobalComponent = false
            var hasGlobalComponentType = false
            
            for row in tableInfo {
                if let columnName = row[1] as? String {
                    if columnName == "is_global_component" {
                        hasIsGlobalComponent = true
                    } else if columnName == "global_component_type" {
                        hasGlobalComponentType = true
                    }
                }
            }
            
            // 添加缺失的字段
            if !hasIsGlobalComponent {
                try db.execute("""
                    ALTER TABLE template_components 
                    ADD COLUMN is_global_component BOOLEAN DEFAULT 0
                """)
                Logger.database.info("✅ 添加字段: is_global_component")
            }
            
            if !hasGlobalComponentType {
                try db.execute("""
                    ALTER TABLE template_components 
                    ADD COLUMN global_component_type TEXT
                """)
                Logger.database.info("✅ 添加字段: global_component_type")
            }
            
        } catch {
            Logger.database.error("❌ 迁移 template_components 表失败: \(error)")
        }
    }
    
}
