//
//  GenericOptionsViewController.swift
//  magicBox
//
//  Created by <PERSON> on 2025/7/15.
//

import UIKit
import SnapKit

class GenericOptionsViewController: BaseViewController {
    // MARK: - Properties
    private var tableView: UITableView!
    private var addButton: UIButton!
    
    private let componentType: ComponentType
    private var options: [ComponentOption] = []
    
    // MARK: - Initialization
    init(componentType: ComponentType) {
        self.componentType = componentType
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupNavigationBar()
        loadOptions()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        // TableView
        tableView = UITableView(frame: .zero, style: .insetGrouped)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(GenericOptionCell.self, forCellReuseIdentifier: "OptionCell")
        tableView.rowHeight = 60
        tableView.backgroundColor = AppTheme.Colors.groupedBackground
        view.addSubview(tableView)
        
        // Add Button
        addButton = UIButton(type: .system)
        addButton.setTitle("添加\(componentType.localizedTitle)", for: .normal)
        addButton.backgroundColor = AppTheme.Colors.primaryButtonBackground
        addButton.setTitleColor(AppTheme.Colors.primaryButtonText, for: .normal)
        addButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        addButton.layer.cornerRadius = 12
        addButton.addTarget(self, action: #selector(addOptionTapped), for: .touchUpInside)
        view.addSubview(addButton)
        
        // Constraints
        tableView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalTo(addButton.snp.top).offset(-16)
        }
        
        addButton.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-16)
            make.height.equalTo(50)
        }
    }
    
    private func setupNavigationBar() {
        title = componentType.localizedTitle
        navigationItem.rightBarButtonItem = editButtonItem
    }
    
    // MARK: - Data Loading
    private func loadOptions() {
        do {
            options = try DatabaseManager.shared.getComponentOptions(componentId: componentType.componentId)
            tableView.reloadData()
        } catch {
            Logger.database.error("加载\(componentType.name)选项失败: \(error)")
            showAlert(title: "错误", message: "加载数据失败")
        }
    }
    
    // MARK: - Actions
    @objc private func addOptionTapped() {
        showAddEditDialog(option: nil)
    }
    
    // MARK: - Edit Mode
    override func setEditing(_ editing: Bool, animated: Bool) {
        super.setEditing(editing, animated: animated)
        tableView.setEditing(editing, animated: animated)
    }
    
    // MARK: - Dialogs
    private func showAddEditDialog(option: ComponentOption?) {
        let isEditing = option != nil
        let title = isEditing ? "编辑\(componentType.localizedTitle)" : "添加\(componentType.localizedTitle)"
        
        let alert = UIAlertController(title: title, message: nil, preferredStyle: .alert)
        
        // Display name field
        alert.addTextField { textField in
            textField.placeholder = "显示名称"
            textField.text = option?.optionDisplay
        }
        
        // Value field (for some types, this might be the same as display)
        if componentType != .category && componentType != .season {
            alert.addTextField { textField in
                textField.placeholder = "值（可选，留空将使用显示名称）"
                textField.text = option?.optionValue
            }
        }
        
        let saveAction = UIAlertAction(title: isEditing ? "保存" : "添加", style: .default) { [weak self] _ in
            guard let self = self,
                  let displayField = alert.textFields?[0],
                  let display = displayField.text?.trimmingCharacters(in: .whitespacesAndNewlines),
                  !display.isEmpty else { return }
            
            let value: String
            if self.componentType == .category || self.componentType == .season {
                value = display.lowercased().replacingOccurrences(of: " ", with: "_")
            } else if let valueField = alert.textFields?[1], 
                      let inputValue = valueField.text?.trimmingCharacters(in: .whitespacesAndNewlines),
                      !inputValue.isEmpty {
                value = inputValue
            } else {
                value = display.lowercased().replacingOccurrences(of: " ", with: "_")
            }
            
            if isEditing, let optionId = option?.id {
                self.updateOption(id: optionId, display: display, value: value, sortOrder: option?.sortOrder ?? 0)
            } else {
                self.addOption(display: display, value: value)
            }
        }
        
        alert.addAction(saveAction)
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        present(alert, animated: true)
    }
    
    // MARK: - Database Operations
    private func addOption(display: String, value: String) {
        do {
            let option = ComponentOption(
                componentId: componentType.componentId,
                optionValue: value,
                optionDisplay: display,
                metadata: PresetData.createMetadata(key: value, category: componentType.name.lowercased()),
                isActive: true,
                sortOrder: options.count
            )
            _ = try DatabaseManager.shared.createComponentOption(option)
            loadOptions()
        } catch {
            Logger.database.error("添加\(componentType.name)选项失败: \(error)")
            showAlert(title: "错误", message: "添加失败")
        }
    }
    
    private func updateOption(id: Int64, display: String, value: String, sortOrder: Int) {
        do {
            // 先获取原选项以保留其他属性
            let existingOptions = options.first { $0.id == id }
            let option = ComponentOption(
                id: id,
                componentId: componentType.componentId,
                optionValue: value,
                optionDisplay: display,
                metadata: existingOptions?.metadata,
                isActive: existingOptions?.isActive ?? true,
                sortOrder: sortOrder,
                createdAt: existingOptions?.createdAt ?? Date()
            )
            _ = try DatabaseManager.shared.updateComponentOption(id: id, option: option)
            loadOptions()
        } catch {
            Logger.database.error("更新\(componentType.name)选项失败: \(error)")
            showAlert(title: "错误", message: "更新失败")
        }
    }
    
    private func deleteOption(at indexPath: IndexPath) {
        let option = options[indexPath.row]
        guard let optionId = option.id else { return }
        
        do {
            _ = try DatabaseManager.shared.deleteComponentOption(id: optionId)
            options.remove(at: indexPath.row)
            tableView.deleteRows(at: [indexPath], with: .fade)
        } catch {
            Logger.database.error("删除\(componentType.name)选项失败: \(error)")
            showAlert(title: "错误", message: "删除失败")
        }
    }
    
    private func updateSortOrder() {
        do {
            for (index, option) in options.enumerated() {
                if let optionId = option.id {
                    let updatedOption = ComponentOption(
                        id: optionId,
                        componentId: option.componentId,
                        optionValue: option.optionValue,
                        optionDisplay: option.optionDisplay,
                        metadata: option.metadata,
                        isActive: option.isActive,
                        sortOrder: index,
                        createdAt: option.createdAt
                    )
                    _ = try DatabaseManager.shared.updateComponentOption(id: optionId, option: updatedOption)
                }
            }
            loadOptions()
        } catch {
            Logger.database.error("更新\(componentType.name)排序失败: \(error)")
            showAlert(title: "错误", message: "更新排序失败")
        }
    }
    
    // MARK: - Helper
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - UITableViewDataSource
extension GenericOptionsViewController: UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return 1
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return options.count
    }
    
    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        return "\(componentType.localizedTitle)选项"
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "OptionCell", for: indexPath) as! GenericOptionCell
        let option = options[indexPath.row]
        cell.configure(with: option, componentType: componentType)
        cell.onEditTapped = { [weak self] in
            self?.showAddEditDialog(option: option)
        }
        return cell
    }
    
    func tableView(_ tableView: UITableView, commit editingStyle: UITableViewCell.EditingStyle, forRowAt indexPath: IndexPath) {
        if editingStyle == .delete {
            deleteOption(at: indexPath)
        }
    }
    
    func tableView(_ tableView: UITableView, canMoveRowAt indexPath: IndexPath) -> Bool {
        return isEditing
    }
    
    func tableView(_ tableView: UITableView, moveRowAt sourceIndexPath: IndexPath, to destinationIndexPath: IndexPath) {
        let movedOption = options.remove(at: sourceIndexPath.row)
        options.insert(movedOption, at: destinationIndexPath.row)
        updateSortOrder()
    }
}

// MARK: - UITableViewDelegate
extension GenericOptionsViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
    }
}

// MARK: - Custom Cell
class GenericOptionCell: UITableViewCell {
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16)
        label.textColor = AppTheme.Colors.label
        return label
    }()
    
    private let valueLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = AppTheme.Colors.secondaryLabel
        return label
    }()
    
    private let editButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "pencil.line"), for: .normal)
        button.tintColor = AppTheme.Colors.label
        button.backgroundColor = .clear
        return button
    }()
    
    var onEditTapped: (() -> Void)?
    
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = AppTheme.Colors.secondaryGroupedBackground
        
        contentView.addSubview(titleLabel)
        contentView.addSubview(valueLabel)
        contentView.addSubview(editButton)
        
        editButton.addTarget(self, action: #selector(editButtonTapped), for: .touchUpInside)
        
        editButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.equalTo(28)
            make.height.equalTo(28)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.trailing.equalTo(editButton.snp.leading).offset(-12)
            make.top.equalToSuperview().offset(12)
        }
        
        valueLabel.snp.makeConstraints { make in
            make.leading.equalTo(titleLabel)
            make.trailing.equalTo(titleLabel)
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
        }
    }
    
    func configure(with option: ComponentOption, componentType: ComponentType) {
        titleLabel.text = option.optionDisplay
        
        if option.optionValue != option.optionDisplay {
            valueLabel.text = option.optionValue
            valueLabel.isHidden = false
        } else {
            valueLabel.isHidden = true
        }
    }
    
    @objc private func editButtonTapped() {
        onEditTapped?()
    }
    
    override func setHighlighted(_ highlighted: Bool, animated: Bool) {
        super.setHighlighted(highlighted, animated: animated)
        let bgColor = highlighted ? AppTheme.Colors.tertiaryGroupedBackground : AppTheme.Colors.secondaryGroupedBackground
        UIView.animate(withDuration: 0.2) {
            self.backgroundColor = bgColor
        }
    }
    
    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)
        let bgColor = selected ? AppTheme.Colors.tertiaryGroupedBackground : AppTheme.Colors.secondaryGroupedBackground
        UIView.animate(withDuration: 0.2) {
            self.backgroundColor = bgColor
        }
    }
}
