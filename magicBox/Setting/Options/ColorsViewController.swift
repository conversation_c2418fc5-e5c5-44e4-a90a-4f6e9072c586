//
//  ColorsViewController.swift
//  magicBox
//
//  Created by xmly on 2025/7/8.
//

import UIKit
import SnapKit

class ColorsViewController: BaseViewController {
    // MARK: - UI组件
    private var tableView: UITableView!
    private var addButton: UIButton!
    
    // MARK: - 数据
    private var colorOptions: [ComponentOption] = []
    private let colorComponentId: Int64 = ComponentType.color.componentId
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupNavigationBar()
        loadColorOptions()
    }
    
    // MARK: - UI设置
    private func setupUI() {
        
        // 设置TableView
        tableView = UITableView(frame: .zero, style: .insetGrouped)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(ColorTableViewCell.self, forCellReuseIdentifier: "ColorCell")
        tableView.rowHeight = 60
        tableView.backgroundColor = AppTheme.Colors.groupedBackground
        view.addSubview(tableView)
        
        // 设置添加按钮
        addButton = UIButton(type: .system)
        addButton.setTitle(NSLocalizedString("colors.add_color", comment: "Add Color"), for: .normal)
        addButton.backgroundColor = AppTheme.Colors.primaryButtonBackground
        addButton.setTitleColor(AppTheme.Colors.primaryButtonText, for: .normal)
        addButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        addButton.layer.cornerRadius = 12
        addButton.addTarget(self, action: #selector(addColorTapped), for: .touchUpInside)
        view.addSubview(addButton)
        
        // 使用SnapKit设置约束
        tableView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalTo(addButton.snp.top).offset(-16)
        }
        
        addButton.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-16)
            make.height.equalTo(50)
        }
    }
    
    private func setupNavigationBar() {
        title = NSLocalizedString("colors.title", comment: "Color Management")
        
        // 添加编辑按钮
        navigationItem.rightBarButtonItem = editButtonItem
    }
    
    // MARK: - 数据加载
    func loadColorOptions() {
        do {
            // 使用ColorManager加载颜色选项
            colorOptions = try ColorManager.shared.getAllColors()
            
            tableView.reloadData()
        } catch {
            print("加载颜色选项失败: \(error)")
            showAlert(title: NSLocalizedString("common.error", comment: "Error"), message: NSLocalizedString("colors.load_error", comment: "Failed to load color data"))
        }
    }
    
    // MARK: - 按钮事件
    @objc private func addColorTapped() {
        showAddColorDialog()
    }
    
    // MARK: - 辅助方法
    private func showAddColorDialog() {
        let alert = UIAlertController(title: NSLocalizedString("colors.add_color_title", comment: "Add New Color"), message: NSLocalizedString("colors.add_color_message", comment: "Please enter color information"), preferredStyle: .alert)
        
        alert.addTextField { textField in
            textField.placeholder = NSLocalizedString("colors.color_name_placeholder", comment: "Color Name")
        }
        
        alert.addTextField { textField in
            textField.placeholder = NSLocalizedString("colors.color_value_placeholder", comment: "Color Value (#RRGGBB)")
        }
        
        let addAction = UIAlertAction(title: NSLocalizedString("common.add", comment: "Add"), style: .default) { [weak self] _ in
            guard let self = self,
                  let nameField = alert.textFields?[0],
                  let valueField = alert.textFields?[1],
                  let colorName = nameField.text, !colorName.isEmpty,
                  let colorValue = valueField.text, !colorValue.isEmpty else {
                return
            }
            
            self.addColor(name: colorName, value: colorValue)
        }
        
        let cancelAction = UIAlertAction(title: NSLocalizedString("common.cancel", comment: "Cancel"), style: .cancel)
        
        alert.addAction(addAction)
        alert.addAction(cancelAction)
        
        present(alert, animated: true)
    }
    
    private func addColor(name: String, value: String) {
        do {
            // 使用ColorManager添加颜色（内部会进行验证）
            _ = try ColorManager.shared.addCustomColor(name: name, value: value)
            loadColorOptions()
        } catch let error as ColorManagerError {
            // 处理ColorManager的特定错误
            showAlert(title: NSLocalizedString("common.error", comment: "Error"), message: error.localizedDescription)
        } catch {
            print("添加颜色失败: \(error)")
            showAlert(title: NSLocalizedString("common.error", comment: "Error"), message: NSLocalizedString("colors.add_error", comment: "Failed to add color"))
        }
    }
    
    // MARK: - 编辑模式
    override func setEditing(_ editing: Bool, animated: Bool) {
        super.setEditing(editing, animated: animated)
        tableView.setEditing(editing, animated: animated)
    }
}

// MARK: - TableView DataSource & Delegate
extension ColorsViewController: UITableViewDataSource, UITableViewDelegate {
    func numberOfSections(in tableView: UITableView) -> Int {
        return 1
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return colorOptions.count
    }
    
    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        return NSLocalizedString("colors.section.title", comment: "Colors")
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "ColorCell", for: indexPath) as! ColorTableViewCell
        let option = colorOptions[indexPath.row]
        cell.configure(with: option)
        cell.onEditTapped = { [weak self] in
            self?.showEditColorDialog(for: option, at: indexPath)
        }
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
    }
    
    func tableView(_ tableView: UITableView, commit editingStyle: UITableViewCell.EditingStyle, forRowAt indexPath: IndexPath) {
        if editingStyle == .delete {
            deleteColor(at: indexPath)
        }
    }
    
    // 添加移动功能
    func tableView(_ tableView: UITableView, canMoveRowAt indexPath: IndexPath) -> Bool {
        return isEditing
    }
    
    func tableView(_ tableView: UITableView, moveRowAt sourceIndexPath: IndexPath, to destinationIndexPath: IndexPath) {
        // 更新数据源
        let movedOption = colorOptions.remove(at: sourceIndexPath.row)
        colorOptions.insert(movedOption, at: destinationIndexPath.row)
        
        // 更新数据库中的排序顺序
        updateColorSortOrder()
    }
    
    private func updateColorSortOrder() {
        do {
            // 更新每个选项的排序顺序
            for (index, option) in colorOptions.enumerated() {
                if let optionId = option.id {
                    let updatedOption = ComponentOption(
                        id: optionId,
                        componentId: option.componentId,
                        optionValue: option.optionValue,
                        optionDisplay: option.optionDisplay,
                        metadata: option.metadata,
                        isActive: option.isActive,
                        sortOrder: index,
                        createdAt: option.createdAt
                    )
                    try DatabaseManager.shared.updateComponentOption(id: optionId, option: updatedOption)
                }
            }
            
            // 重新加载数据以确保显示正确的顺序
            loadColorOptions()
        } catch {
            print("更新颜色排序失败: \(error)")
            showAlert(title: NSLocalizedString("common.error", comment: "Error"), message: NSLocalizedString("colors.update_error", comment: "Failed to update color"))
        }
    }

    private func showEditColorDialog(for option: ComponentOption, at indexPath: IndexPath) {
        let alert = UIAlertController(title: NSLocalizedString("colors.edit_color_title", comment: "Edit Color"), message: NSLocalizedString("colors.edit_color_message", comment: "Modify color information"), preferredStyle: .alert)
        
        alert.addTextField { textField in
            textField.text = option.optionDisplay
            textField.placeholder = NSLocalizedString("colors.color_name_placeholder", comment: "Color Name")
        }
        
        alert.addTextField { textField in
            textField.text = option.optionValue
            textField.placeholder = NSLocalizedString("colors.color_value_placeholder", comment: "Color Value (#RRGGBB)")
        }
        
        let saveAction = UIAlertAction(title: NSLocalizedString("common.save", comment: "Save"), style: .default) { [weak self] _ in
            guard let self = self,
                  let nameField = alert.textFields?[0],
                  let valueField = alert.textFields?[1],
                  let colorName = nameField.text, !colorName.isEmpty,
                  let colorValue = valueField.text, !colorValue.isEmpty,
                  let optionId = option.id else {
                return
            }
            
            self.updateColor(name: colorName, value: colorValue, optionId: optionId)
        }
        
        let cancelAction = UIAlertAction(title: NSLocalizedString("common.cancel", comment: "Cancel"), style: .cancel)
        
        alert.addAction(saveAction)
        alert.addAction(cancelAction)
        
        present(alert, animated: true)
    }
}

// MARK: - 数据库相关操作
extension ColorsViewController {
    // 删除颜色
    func deleteColor(at indexPath: IndexPath) {
        let option = colorOptions[indexPath.row]
        do {
            if let optionId = option.id {
                _ = try ColorManager.shared.deleteColor(id: optionId)
                colorOptions.remove(at: indexPath.row)
                tableView.deleteRows(at: [indexPath], with: .fade)
            }
        } catch {
            print("删除颜色失败: \(error)")
            showAlert(title: NSLocalizedString("common.error", comment: "Error"), message: NSLocalizedString("colors.delete_error", comment: "Failed to delete color"))
        }
    }
    
    // 更新颜色
    func updateColor(name: String, value: String, optionId: Int64) {
        do {
            _ = try ColorManager.shared.updateColor(id: optionId, name: name, value: value)
            loadColorOptions()
        } catch let error as ColorManagerError {
            showAlert(title: NSLocalizedString("common.error", comment: "Error"), message: error.localizedDescription)
        } catch {
            print("更新颜色失败: \(error)")
            showAlert(title: NSLocalizedString("common.error", comment: "Error"), message: NSLocalizedString("colors.update_error", comment: "Failed to update color"))
        }
    }
}

// MARK: - 自定义Cell
class ColorTableViewCell: UITableViewCell {
    
    private let colorView: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 10
        view.layer.borderWidth = 1
        view.layer.borderColor = AppTheme.Colors.cardBorder.cgColor
        return view
    }()
    
    private let colorLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16)
        label.textColor = AppTheme.Colors.label
        return label
    }()
    
    private let editButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "pencil.line"), for: .normal)
        button.tintColor = AppTheme.Colors.label
        button.backgroundColor = .clear
        return button
    }()
    
    var onEditTapped: (() -> Void)?
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = AppTheme.Colors.secondaryGroupedBackground
        contentView.backgroundColor = AppTheme.Colors.secondaryGroupedBackground
        
        contentView.addSubview(colorView)
        contentView.addSubview(colorLabel)
        contentView.addSubview(editButton)
        
        editButton.addTarget(self, action: #selector(editButtonTapped), for: .touchUpInside)
        
        // 使用SnapKit设置约束
        editButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.equalTo(28)
            make.height.equalTo(28)
        }
        
        colorView.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(36)
        }
        
        colorLabel.snp.makeConstraints { make in
            make.leading.equalTo(colorView.snp.trailing).offset(12)
            make.centerY.equalToSuperview()
            make.trailing.equalTo(editButton.snp.leading).offset(-12)
        }
    }
    
    @objc private func editButtonTapped() {
        onEditTapped?()
    }
    
    override func setHighlighted(_ highlighted: Bool, animated: Bool) {
        super.setHighlighted(highlighted, animated: animated)
        let newBgColor: UIColor = highlighted ? AppTheme.Colors.tertiaryGroupedBackground : AppTheme.Colors.secondaryGroupedBackground
        UIView.animate(withDuration: 0.2) {
            self.backgroundColor = newBgColor
            self.contentView.backgroundColor = newBgColor
        }
    }
    
    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)
        let newBgColor: UIColor = selected ? AppTheme.Colors.tertiaryGroupedBackground : AppTheme.Colors.secondaryGroupedBackground
        UIView.animate(withDuration: 0.2) {
            self.backgroundColor = newBgColor
            self.contentView.backgroundColor = newBgColor
        }
    }
    
    func configure(with option: ComponentOption) {
        colorLabel.text = option.optionDisplay
        
        // 设置颜色方块的颜色
        if let color = UIColor(hex: option.optionValue) {
            colorView.backgroundColor = color
        } else {
            colorView.backgroundColor = UIColor.systemGray5
        }
    }
}

// MARK: - 提示框
fileprivate extension ColorsViewController {
    func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: NSLocalizedString("common.ok", comment: "OK"), style: .default))
        present(alert, animated: true)
    }
}
