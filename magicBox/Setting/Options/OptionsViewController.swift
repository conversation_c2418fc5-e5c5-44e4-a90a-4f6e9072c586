//
//  OptionsViewController.swift
//  magicBox
//
//  Created by xmly on 2025/7/8.
//

import UIKit
import SnapKit

class OptionsViewController: BaseViewController {
    // MARK: - UI组件
    private var tableView: UITableView!
    
    // MARK: - 数据
    private struct OptionSection {
        let title: String
        let items: [OptionItem]
    }
    
    private struct OptionItem {
        let title: String
        let subtitle: String
        let icon: String
        let action: () -> Void
    }
    
    private var sections: [OptionSection] = []
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupNavigationBar()
        setupData()
    }
    
    // MARK: - UI设置
    private func setupUI() {
        
        // 设置TableView
        tableView = UITableView(frame: .zero, style: .insetGrouped)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "OptionCell")
        tableView.backgroundColor = AppTheme.Colors.groupedBackground
        view.addSubview(tableView)
        
        // 使用SnapKit设置约束
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    private func setupNavigationBar() {
        title = NSLocalizedString("options.title", comment: "Options Management")
    }
    
    private func setupData() {
        sections = [
            OptionSection(title: NSLocalizedString("options.appearance.title", comment: "Appearance"), items: [
                OptionItem(
                    title: NSLocalizedString("options.colors.title", comment: "Colors"),
                    subtitle: NSLocalizedString("options.colors.subtitle", comment: "Manage color options"),
                    icon: "paintpalette.fill",
                    action: { [weak self] in
                        self?.showColorOptions()
                    }
                )
            ]),
            OptionSection(title: NSLocalizedString("options.clothing.title", comment: "Clothing Attributes"), items: [
                OptionItem(
                    title: NSLocalizedString("options.brands.title", comment: "Brands"),
                    subtitle: NSLocalizedString("options.brands.subtitle", comment: "Manage brand options"),
                    icon: "tag.fill",
                    action: { [weak self] in
                        self?.showGenericOptions(componentType: .brand)
                    }
                ),
                OptionItem(
                    title: NSLocalizedString("options.sizes.title", comment: "Sizes"),
                    subtitle: NSLocalizedString("options.sizes.subtitle", comment: "Manage size options"),
                    icon: "ruler.fill",
                    action: { [weak self] in
                        self?.showGenericOptions(componentType: .size)
                    }
                ),
                OptionItem(
                    title: NSLocalizedString("options.materials.title", comment: "Materials"),
                    subtitle: NSLocalizedString("options.materials.subtitle", comment: "Manage material options"),
                    icon: "square.layers.3d.down.right.fill",
                    action: { [weak self] in
                        self?.showGenericOptions(componentType: .material)
                    }
                ),
                OptionItem(
                    title: NSLocalizedString("options.conditions.title", comment: "Conditions"),
                    subtitle: NSLocalizedString("options.conditions.subtitle", comment: "Manage condition options"),
                    icon: "star.fill",
                    action: { [weak self] in
                        self?.showGenericOptions(componentType: .condition)
                    }
                ),
                OptionItem(
                    title: NSLocalizedString("options.seasons.title", comment: "Seasons"),
                    subtitle: NSLocalizedString("options.seasons.subtitle", comment: "Manage season options"),
                    icon: "cloud.sun.fill",
                    action: { [weak self] in
                        self?.showGenericOptions(componentType: .season)
                    }
                ),
                OptionItem(
                    title: NSLocalizedString("options.categories.title", comment: "Categories"),
                    subtitle: NSLocalizedString("options.categories.subtitle", comment: "Manage category options"),
                    icon: "folder.fill",
                    action: { [weak self] in
                        self?.showGenericOptions(componentType: .category)
                    }
                )
            ])
        ]
    }
    
    // MARK: - 导航
    private func showColorOptions() {
        let colorVC = ColorsViewController()
        navigationController?.pushViewController(colorVC, animated: true)
    }
    
    private func showGenericOptions(componentType: ComponentType) {
        let genericVC = GenericOptionsViewController(componentType: componentType)
        navigationController?.pushViewController(genericVC, animated: true)
    }
}

// MARK: - UITableViewDataSource
extension OptionsViewController: UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return sections.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return sections[section].items.count
    }
    
    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        return sections[section].title
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "OptionCell", for: indexPath)
        let item = sections[indexPath.section].items[indexPath.row]
        
        var config = cell.defaultContentConfiguration()
        config.text = item.title
        config.secondaryText = item.subtitle
        config.image = UIImage(systemName: item.icon)
        
        cell.contentConfiguration = config
        cell.accessoryType = .disclosureIndicator
        
        return cell
    }
}

// MARK: - UITableViewDelegate
extension OptionsViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        let item = sections[indexPath.section].items[indexPath.row]
        item.action()
    }
} 
