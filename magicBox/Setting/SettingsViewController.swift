//
//  SettingsViewController.swift
//  magicBox
//
//  Created by <PERSON><PERSON><PERSON> <PERSON>an on 2025/5/18.
//

import UIKit
import SnapKit

class SettingsViewController: BaseViewController {
    
    private var tableView: UITableView!
    
    // 设置选项数据结构
    struct SettingSection {
        let title: String
        let items: [SettingItem]
    }
    
    struct SettingItem {
        let title: String
        let subtitle: String?
        let icon: String
        let type: SettingType
        let action: (() -> Void)?
    }
    
    enum SettingType {
        case toggle(isOn: Bool)
        case disclosure
        case info(value: String)
    }
    
    private var settingSections: [SettingSection] = []

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupSettingsData()
    }
    
    private func setupUI() {
        title = NSLocalizedString("settings.title", comment: "Settings")
        
        // 创建TableView
        tableView = UITableView(frame: .zero, style: .insetGrouped)
        tableView.delegate = self
        tableView.dataSource = self
        
        // 注册cell
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "SettingCell")
        tableView.register(SwitchTableViewCell.self, forCellReuseIdentifier: "SwitchCell")
        
        view.addSubview(tableView)
        
        tableView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.leading.trailing.bottom.equalToSuperview()
        }
    }
    
    private func setupSettingsData() {
        settingSections = [
            SettingSection(title: NSLocalizedString("settings.notification.title", comment: "Notification Settings"), items: [
                SettingItem(title: NSLocalizedString("settings.notification.push_notification", comment: "Push Notifications"), subtitle: NSLocalizedString("settings.notification.push_notification_subtitle", comment: "Receive important reminders"), icon: "bell.fill", type: .toggle(isOn: true)) {
                    self.toggleNotifications()
                },
                SettingItem(title: NSLocalizedString("settings.notification.calendar_reminder", comment: "Calendar Reminders"), subtitle: NSLocalizedString("settings.notification.calendar_reminder_subtitle", comment: "Schedule event reminders"), icon: "calendar.badge.clock", type: .toggle(isOn: true)) {
                    self.toggleCalendarReminders()
                }
            ]),
            
            SettingSection(title: NSLocalizedString("settings.appearance.title", comment: "Appearance Settings"), items: [
                SettingItem(title: NSLocalizedString("settings.appearance.dark_mode", comment: "Dark Mode"), subtitle: NSLocalizedString("settings.appearance.dark_mode_subtitle", comment: "Follow system or manual setting"), icon: "moon.fill", type: .disclosure) {
                    self.showThemeSettings()
                },
                SettingItem(title: NSLocalizedString("settings.appearance.font_size", comment: "Font Size"), subtitle: NSLocalizedString("settings.appearance.font_size_subtitle", comment: "Adjust app font size"), icon: "textformat.size", type: .disclosure) {
                    self.showFontSettings()
                },
                SettingItem(title: NSLocalizedString("settings.appearance.options", comment: "Options Management"), subtitle: NSLocalizedString("settings.appearance.options_subtitle", comment: "Manage app options like colors, sizes, etc."), icon: "slider.horizontal.3", type: .disclosure) {
                    self.showOptionsManagement()
                }
            ]),
            
            SettingSection(title: NSLocalizedString("settings.data.title", comment: "Data Management"), items: [
                SettingItem(title: NSLocalizedString("settings.data.sync", comment: "Data Sync"), subtitle: NSLocalizedString("settings.data.sync_subtitle", comment: "iCloud sync"), icon: "icloud.fill", type: .toggle(isOn: false)) {
                    self.toggleiCloudSync()
                },
                SettingItem(title: NSLocalizedString("settings.data.clear_cache", comment: "Clear Cache"), subtitle: NSLocalizedString("settings.data.clear_cache_subtitle", comment: "Free up storage space"), icon: "trash.fill", type: .disclosure) {
                    self.clearCache()
                },
                SettingItem(title: NSLocalizedString("settings.data.export_data", comment: "Export Data"), subtitle: NSLocalizedString("settings.data.export_data_subtitle", comment: "Backup your data"), icon: "square.and.arrow.up.fill", type: .disclosure) {
                    self.exportData()
                }
            ]),
            
            SettingSection(title: NSLocalizedString("settings.security.title", comment: "Security Settings"), items: [
                SettingItem(title: NSLocalizedString("settings.security.biometric_auth", comment: "Biometric Authentication"), subtitle: NSLocalizedString("settings.security.biometric_auth_subtitle", comment: "Use Face ID or Touch ID"), icon: "faceid", type: .toggle(isOn: false)) {
                    self.toggleBiometrics()
                },
                SettingItem(title: NSLocalizedString("settings.about.privacy_policy", comment: "Privacy Policy"), subtitle: nil, icon: "hand.raised.fill", type: .disclosure) {
                    self.showPrivacyPolicy()
                }
            ]),
            
            SettingSection(title: NSLocalizedString("settings.about.title", comment: "About"), items: [
                SettingItem(title: NSLocalizedString("settings.about.version", comment: "Version"), subtitle: nil, icon: "info.circle.fill", type: .info(value: "1.0.0"), action: nil),
                SettingItem(title: NSLocalizedString("settings.about.feedback", comment: "Feedback"), subtitle: NSLocalizedString("settings.about.feedback_subtitle", comment: "Send suggestions and feedback"), icon: "envelope.fill", type: .disclosure) {
                    self.sendFeedback()
                },
                SettingItem(title: NSLocalizedString("settings.about.rate_app", comment: "Rate App"), subtitle: NSLocalizedString("settings.about.rate_app_subtitle", comment: "Rate us on the App Store"), icon: "star.fill", type: .disclosure) {
                    self.rateApp()
                }
            ])
        ]
    }
}

// MARK: - UITableView DataSource & Delegate
extension SettingsViewController: UITableViewDataSource, UITableViewDelegate {
    
    func numberOfSections(in tableView: UITableView) -> Int {
        return settingSections.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return settingSections[section].items.count
    }
    
    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        return settingSections[section].title
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let item = settingSections[indexPath.section].items[indexPath.row]
        
        switch item.type {
        case .toggle(let isOn):
            let cell = tableView.dequeueReusableCell(withIdentifier: "SwitchCell", for: indexPath) as! SwitchTableViewCell
            cell.configure(with: item, isOn: isOn)
            return cell
            
        case .disclosure, .info:
            let cell = tableView.dequeueReusableCell(withIdentifier: "SettingCell", for: indexPath)
            cell.textLabel?.text = item.title
            cell.detailTextLabel?.text = item.subtitle
            cell.imageView?.image = UIImage(systemName: item.icon)
            cell.imageView?.tintColor = AppTheme.Colors.primary
            
            switch item.type {
            case .disclosure:
                cell.accessoryType = .disclosureIndicator
            case .info(let value):
                cell.detailTextLabel?.text = value
                cell.detailTextLabel?.textColor = AppTheme.Colors.secondaryLabel
                cell.selectionStyle = .none
            default:
                break
            }
            
            return cell
        }
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let item = settingSections[indexPath.section].items[indexPath.row]
        item.action?()
    }
}

// MARK: - Setting Actions
extension SettingsViewController {
    
    private func toggleNotifications() {
        let alert = UIAlertController(title: NSLocalizedString("settings.notification.enable_title", comment: "Notification Settings"), message: NSLocalizedString("settings.notification.enable_message", comment: "Enable push notifications?"), preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: NSLocalizedString("common.cancel", comment: "Cancel"), style: .cancel))
        alert.addAction(UIAlertAction(title: NSLocalizedString("common.ok", comment: "OK"), style: .default))
        present(alert, animated: true)
    }
    
    private func toggleCalendarReminders() {
        print("切换日历提醒设置")
    }
    
    private func showThemeSettings() {
        let alert = UIAlertController(title: NSLocalizedString("settings.theme.title", comment: "Appearance Settings"), message: NSLocalizedString("settings.theme.message", comment: "Select app theme"), preferredStyle: .actionSheet)
        alert.addAction(UIAlertAction(title: NSLocalizedString("settings.theme.follow_system", comment: "Follow System"), style: .default))
        alert.addAction(UIAlertAction(title: NSLocalizedString("settings.theme.light_mode", comment: "Light Mode"), style: .default))
        alert.addAction(UIAlertAction(title: NSLocalizedString("settings.theme.dark_mode", comment: "Dark Mode"), style: .default))
        alert.addAction(UIAlertAction(title: NSLocalizedString("common.cancel", comment: "Cancel"), style: .cancel))
        present(alert, animated: true)
    }
    
    private func showFontSettings() {
        let alert = UIAlertController(title: NSLocalizedString("settings.font.title", comment: "Font Settings"), message: NSLocalizedString("settings.font.message", comment: "Adjust font size"), preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: NSLocalizedString("common.ok", comment: "OK"), style: .default))
        present(alert, animated: true)
    }
    
    private func showOptionsManagement() {
        let optionsVC = OptionsViewController()
        navigationController?.pushViewController(optionsVC, animated: true)
    }
    
    private func toggleiCloudSync() {
        print("切换iCloud同步")
    }
    
    private func clearCache() {
        let alert = UIAlertController(title: NSLocalizedString("settings.cache.clear_title", comment: "Clear Cache"), message: NSLocalizedString("settings.cache.clear_message", comment: "Are you sure you want to clear all cache data?"), preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: NSLocalizedString("common.cancel", comment: "Cancel"), style: .cancel))
        alert.addAction(UIAlertAction(title: NSLocalizedString("settings.cache.clear_action", comment: "Clear"), style: .destructive) { _ in
            // 执行清除缓存操作
            self.showSuccessAlert(message: NSLocalizedString("settings.cache.cleared_message", comment: "Cache has been cleared"))
        })
        present(alert, animated: true)
    }
    
    private func exportData() {
        let alert = UIAlertController(title: NSLocalizedString("settings.export.title", comment: "Export Data"), message: NSLocalizedString("settings.export.message", comment: "Export data as JSON format"), preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: NSLocalizedString("common.cancel", comment: "Cancel"), style: .cancel))
        alert.addAction(UIAlertAction(title: NSLocalizedString("settings.export.action", comment: "Export"), style: .default))
        present(alert, animated: true)
    }
    
    private func toggleBiometrics() {
        print("切换生物识别设置")
    }
    
    private func showPrivacyPolicy() {
        let alert = UIAlertController(title: NSLocalizedString("settings.privacy.title", comment: "Privacy Policy"), message: NSLocalizedString("settings.privacy.message", comment: "View our privacy policy"), preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: NSLocalizedString("common.ok", comment: "OK"), style: .default))
        present(alert, animated: true)
    }
    
    private func sendFeedback() {
        let alert = UIAlertController(title: NSLocalizedString("settings.feedback.title", comment: "Feedback"), message: NSLocalizedString("settings.feedback.message", comment: "Thank you for your feedback!"), preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: NSLocalizedString("common.ok", comment: "OK"), style: .default))
        present(alert, animated: true)
    }
    
    private func rateApp() {
        let alert = UIAlertController(title: NSLocalizedString("settings.rate.title", comment: "Rate App"), message: NSLocalizedString("settings.rate.message", comment: "If you like this app, please rate us on the App Store!"), preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: NSLocalizedString("settings.rate.later", comment: "Remind Later"), style: .cancel))
        alert.addAction(UIAlertAction(title: NSLocalizedString("settings.rate.rate_now", comment: "Rate Now"), style: .default))
        present(alert, animated: true)
    }
    
    private func showSuccessAlert(message: String) {
        let alert = UIAlertController(title: NSLocalizedString("common.success", comment: "Success"), message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: NSLocalizedString("common.ok", comment: "OK"), style: .default))
        present(alert, animated: true)
    }
}

// MARK: - Custom Switch Cell
class SwitchTableViewCell: UITableViewCell {
    
    private let switchControl = UISwitch()
    private var action: (() -> Void)?
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: .subtitle, reuseIdentifier: reuseIdentifier)
        setupSwitch()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupSwitch() {
        switchControl.addTarget(self, action: #selector(switchValueChanged), for: .valueChanged)
        accessoryView = switchControl
        selectionStyle = .none
    }
    
    func configure(with item: SettingsViewController.SettingItem, isOn: Bool) {
        textLabel?.text = item.title
        detailTextLabel?.text = item.subtitle
        imageView?.image = UIImage(systemName: item.icon)
        imageView?.tintColor = .systemBlue
        switchControl.isOn = isOn
        self.action = item.action
    }
    
    @objc private func switchValueChanged() {
        action?()
    }
} 
