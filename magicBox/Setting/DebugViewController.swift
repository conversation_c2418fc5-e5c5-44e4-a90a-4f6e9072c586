//
//  DebugViewController.swift
//  magicBox
//
//  Created by AI Assistant on 2025/7/16.
//

import UIKit
import SnapKit

class DebugViewController: BaseViewController {
    
    private var tableView: UITableView!
    
    // 调试选项数据结构
    struct DebugItem {
        let title: String
        let subtitle: String?
        let icon: String
        let action: () -> Void
    }
    
    private var debugItems: [DebugItem] = []

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupDebugData()
    }
    
    private func setupUI() {
        title = "调试页面"
        
        // 创建TableView
        tableView = UITableView(frame: .zero, style: .insetGrouped)
        tableView.delegate = self
        tableView.dataSource = self
        
        // 注册cell
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "DebugCell")
        
        view.addSubview(tableView)
        
        tableView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.leading.trailing.bottom.equalToSuperview()
        }
    }
    
    private func setupDebugData() {
        debugItems = [
            DebugItem(
                title: "底部弹窗示例",
                subtitle: "测试底部半屏视图组件",
                icon: "rectangle.bottomthird.inset.filled"
            ) {
                self.showBottomSheetExamples()
            },
            DebugItem(
                title: "HeaderView样式测试",
                subtitle: "测试不同的HeaderView样式",
                icon: "rectangle.topthird.inset.filled"
            ) {
                self.showHeaderStyleExamples()
            },
            DebugItem(
                title: "UI组件测试",
                subtitle: "测试各种UI组件",
                icon: "paintbrush.fill"
            ) {
                self.showUIComponentsTest()
            },
            DebugItem(
                title: "网络请求测试",
                subtitle: "测试网络请求功能",
                icon: "network"
            ) {
                self.showNetworkTest()
            },
            DebugItem(
                title: "数据库测试",
                subtitle: "测试数据库操作",
                icon: "cylinder.fill"
            ) {
                self.showDatabaseTest()
            }
        ]
    }
}

// MARK: - UITableView DataSource & Delegate
extension DebugViewController: UITableViewDataSource, UITableViewDelegate {
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return debugItems.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "DebugCell", for: indexPath)
        let item = debugItems[indexPath.row]
        
        cell.textLabel?.text = item.title
        cell.detailTextLabel?.text = item.subtitle
        cell.imageView?.image = UIImage(systemName: item.icon)
        cell.imageView?.tintColor = AppTheme.Colors.primary
        cell.accessoryType = .disclosureIndicator
        
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let item = debugItems[indexPath.row]
        item.action()
    }
}

// MARK: - Debug Actions
extension DebugViewController {
    
    private func showBottomSheetExamples() {
        let alert = UIAlertController(
            title: "底部半屏视图示例",
            message: "选择一个示例来查看效果",
            preferredStyle: .actionSheet
        )
        
        alert.addAction(UIAlertAction(
            title: "简单文本示例",
            style: .default,
            handler: { [weak self] _ in
                guard let self = self else { return }
                BottomSheetExample.showSimpleTextExample(from: self)
            }
        ))
        
        alert.addAction(UIAlertAction(
            title: "表单输入示例",
            style: .default,
            handler: { [weak self] _ in
                guard let self = self else { return }
                BottomSheetExample.showFormExample(from: self)
            }
        ))
        
        alert.addAction(UIAlertAction(
            title: "选择列表示例",
            style: .default,
            handler: { [weak self] _ in
                guard let self = self else { return }
                BottomSheetExample.showSelectionExample(from: self)
            }
        ))
        
        alert.addAction(UIAlertAction(
            title: "自定义配置示例",
            style: .default,
            handler: { [weak self] _ in
                guard let self = self else { return }
                BottomSheetExample.showCustomConfigExample(from: self)
            }
        ))
        
        alert.addAction(UIAlertAction(
            title: "取消",
            style: .cancel
        ))
        
        // 在 iPad 上需要设置弹出位置
        if let popoverController = alert.popoverPresentationController {
            popoverController.sourceView = view
            popoverController.sourceRect = CGRect(
                x: view.bounds.midX,
                y: view.bounds.midY,
                width: 1,
                height: 1
            )
        }
        
        present(alert, animated: true)
    }

    private func showHeaderStyleExamples() {
        let alert = UIAlertController(
            title: "HeaderView样式测试",
            message: "选择一个HeaderView样式来查看效果",
            preferredStyle: .actionSheet
        )

        alert.addAction(UIAlertAction(
            title: "标准样式 (取消+标题+确定)",
            style: .default,
            handler: { [weak self] _ in
                self?.showHeaderStyleExample(.standard)
            }
        ))

        alert.addAction(UIAlertAction(
            title: "简洁样式 (标题+确定)",
            style: .default,
            handler: { [weak self] _ in
                self?.showHeaderStyleExample(.simple)
            }
        ))

        alert.addAction(UIAlertAction(
            title: "标题+关闭样式",
            style: .default,
            handler: { [weak self] _ in
                self?.showHeaderStyleExample(.titleOnly)
            }
        ))

        alert.addAction(UIAlertAction(
            title: "纯标题样式 (无按钮)",
            style: .default,
            handler: { [weak self] _ in
                self?.showHeaderStyleExample(.titleWithoutButtons)
            }
        ))

        alert.addAction(UIAlertAction(
            title: "测试背景点击关闭",
            style: .default,
            handler: { [weak self] _ in
                self?.showBackgroundTapExample()
            }
        ))

        alert.addAction(UIAlertAction(
            title: "取消",
            style: .cancel
        ))

        // 在 iPad 上需要设置弹出位置
        if let popoverController = alert.popoverPresentationController {
            popoverController.sourceView = view
            popoverController.sourceRect = CGRect(
                x: view.bounds.midX,
                y: view.bounds.midY,
                width: 1,
                height: 1
            )
        }

        present(alert, animated: true)
    }

    private func showHeaderStyleExample(_ style: BottomSheetHeaderStyle) {
        // 创建一个简单的内容视图
        let contentView = UIView()
        contentView.backgroundColor = AppTheme.Colors.background

        let label = UILabel()
        label.text = "这是一个测试内容视图\n用于展示不同的HeaderView样式\n\n当前样式: \(style)"
        label.numberOfLines = 0
        label.textAlignment = .center
        label.textColor = AppTheme.Colors.label
        label.font = UIFont.systemFont(ofSize: 16)
        contentView.addSubview(label)

        label.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(20)
        }

        // 设置内容视图的高度
        contentView.snp.makeConstraints { make in
            make.height.equalTo(200)
        }

        // 创建配置
        var config = BottomSheetManager.Configuration()
        config.title = "HeaderView样式测试"
        config.headerStyle = style
        config.cancelTitle = "取消"
        config.confirmTitle = "确定"

        // 显示底部半屏视图
        let manager = BottomSheetManager(configuration: config)
        manager.show(
            customView: contentView,
            from: self,
            cancelAction: {
                print("取消按钮被点击")
            },
            confirmAction: {
                print("确定按钮被点击")
            }
        )
    }

    private func showBackgroundTapExample() {
        // 创建一个简单的内容视图
        let contentView = UIView()
        contentView.backgroundColor = AppTheme.Colors.background

        let label = UILabel()
        label.text = "这是一个测试背景点击关闭的示例\n\n点击背景遮罩可以关闭弹窗\n\n这个功能默认是关闭的，需要手动开启"
        label.numberOfLines = 0
        label.textAlignment = .center
        label.textColor = AppTheme.Colors.label
        label.font = UIFont.systemFont(ofSize: 16)
        contentView.addSubview(label)

        label.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(20)
        }

        // 设置内容视图的高度
        contentView.snp.makeConstraints { make in
            make.height.equalTo(200)
        }

        // 创建配置，开启背景点击关闭
        var config = BottomSheetManager.Configuration()
        config.title = "背景点击关闭测试"
        config.headerStyle = .simple
        config.confirmTitle = "确定"
        config.dismissOnBackgroundTap = true  // 开启背景点击关闭

        // 显示底部半屏视图
        let manager = BottomSheetManager(configuration: config)
        manager.show(
            customView: contentView,
            from: self,
            confirmAction: {
                print("确定按钮被点击")
            }
        )
    }

    private func showUIComponentsTest() {
        let alert = UIAlertController(
            title: "UI组件测试",
            message: "选择要测试的UI组件",
            preferredStyle: .actionSheet
        )

        alert.addAction(UIAlertAction(
            title: "按钮样式测试",
            style: .default,
            handler: { [weak self] _ in
                self?.showAlert(title: "按钮测试", message: "这里可以添加按钮样式测试")
            }
        ))

        alert.addAction(UIAlertAction(
            title: "颜色主题测试",
            style: .default,
            handler: { [weak self] _ in
                self?.showAlert(title: "颜色测试", message: "这里可以添加颜色主题测试")
            }
        ))

        alert.addAction(UIAlertAction(title: "取消", style: .cancel))

        if let popoverController = alert.popoverPresentationController {
            popoverController.sourceView = view
            popoverController.sourceRect = CGRect(x: view.bounds.midX, y: view.bounds.midY, width: 1, height: 1)
        }

        present(alert, animated: true)
    }

    private func showNetworkTest() {
        showAlert(title: "网络测试", message: "这里可以添加网络请求测试功能")
    }

    private func showDatabaseTest() {
        showAlert(title: "数据库测试", message: "这里可以添加数据库操作测试功能")
    }

    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}
