import UIKit
import SnapKit

/// 底部半屏视图内容协议
public protocol BottomSheetContentProtocol: AnyObject {
    /// 获取用户选择的数据
    /// - Returns: 返回用户选择的数据，如果没有选择则返回nil
    func getSelectedData() -> Any?

    /// 验证当前选择是否有效（可选实现）
    /// - Returns: 如果当前选择有效返回true，否则返回false
    func validateSelection() -> Bool

    /// 重置选择状态（可选实现）
    func resetSelection()
}

/// 为协议提供默认实现
public extension BottomSheetContentProtocol {
    func validateSelection() -> Bool {
        return true // 默认总是有效
    }

    func resetSelection() {
        // 默认空实现
    }
}

/// 底部半屏视图管理器
public class BottomSheetManager {
    
    // MARK: - 类型定义
    
    /// 按钮点击回调类型
    public typealias ButtonAction = () -> Void

    /// 确认按钮回调类型（带数据）
    public typealias ConfirmActionWithData = (Any?) -> Void
    
    /// 配置结构体
    public struct Configuration {
        /// 取消按钮标题
        public var cancelTitle: String = NSLocalizedString("common.cancel", comment: "取消")
        /// 确定按钮标题
        public var confirmTitle: String = NSLocalizedString("common.confirm", comment: "确定")
        /// 标题文本
        public var title: String?
        /// 是否显示标题
        public var showTitle: Bool = true
        /// 头部视图高度
        public var headerHeight: CGFloat = 60
        /// 圆角半径
        public var cornerRadius: CGFloat = 16
        /// 背景遮罩透明度
        public var maskAlpha: CGFloat = 0.5
        /// 动画时长
        public var animationDuration: TimeInterval = 0.3
        /// 内容视图边距
        public var contentInsets: UIEdgeInsets = UIEdgeInsets(top: 16, left: 16, bottom: 16, right: 16)
        
        public init() {}
    }
    
    // MARK: - 私有属性
    
    private var containerView: UIView!
    private var backgroundMaskView: UIView!
    private var contentView: UIView!
    private var headerView: UIView!
    private var customContentView: UIView!
    
    private var cancelButton: UIButton!
    private var confirmButton: UIButton!
    private var titleLabel: UILabel!
    
    private var configuration: Configuration
    private var cancelAction: ButtonAction?
    private var confirmAction: ButtonAction?
    private var confirmActionWithData: ConfirmActionWithData?

    private weak var presentingViewController: UIViewController?

    // 用于保持实例的强引用，防止过早释放
    private static var activeManagers: [BottomSheetManager] = []
    
    // MARK: - 初始化
    
    /// 初始化底部半屏视图管理器
    /// - Parameter configuration: 配置参数
    public init(configuration: Configuration = Configuration()) {
        self.configuration = configuration
    }
    
    // MARK: - 公开方法
    
    /// 显示底部半屏视图
    /// - Parameters:
    ///   - customView: 自定义内容视图
    ///   - from: 展示的视图控制器
    ///   - cancelAction: 取消按钮回调
    ///   - confirmAction: 确定按钮回调
    public func show(
        customView: UIView,
        from viewController: UIViewController,
        cancelAction: ButtonAction? = nil,
        confirmAction: ButtonAction? = nil
    ) {
        self.presentingViewController = viewController
        self.cancelAction = cancelAction
        self.confirmAction = confirmAction
        self.confirmActionWithData = nil
        self.customContentView = customView

        // 添加到活跃管理器列表，防止被释放
        BottomSheetManager.activeManagers.append(self)

        setupViews()
        setupConstraints()
        presentWithAnimation()
    }

    /// 显示底部半屏视图（支持数据回调）
    /// - Parameters:
    ///   - customView: 自定义内容视图（需要实现BottomSheetContentProtocol协议）
    ///   - from: 展示的视图控制器
    ///   - cancelAction: 取消按钮回调
    ///   - confirmAction: 确定按钮回调，会传入选中的数据
    public func showWithDataCallback(
        customView: UIView & BottomSheetContentProtocol,
        from viewController: UIViewController,
        cancelAction: ButtonAction? = nil,
        confirmAction: @escaping ConfirmActionWithData
    ) {
        self.presentingViewController = viewController
        self.cancelAction = cancelAction
        self.confirmAction = nil
        self.confirmActionWithData = confirmAction
        self.customContentView = customView

        // 添加到活跃管理器列表，防止被释放
        BottomSheetManager.activeManagers.append(self)

        setupViews()
        setupConstraints()
        presentWithAnimation()
    }
    
    /// 隐藏底部半屏视图
    public func dismiss() {
        dismissWithAnimation()
    }
}

// MARK: - 私有方法 - UI设置
private extension BottomSheetManager {
    
    func setupViews() {
        guard let presentingVC = presentingViewController else { return }

        // 确定要添加视图的目标视图控制器
        let targetViewController = getTargetViewController(from: presentingVC)

        // 创建容器视图
        containerView = UIView()
        containerView.backgroundColor = UIColor.clear
        targetViewController.view.addSubview(containerView)
        
        // 创建背景遮罩
        backgroundMaskView = UIView()
        backgroundMaskView.backgroundColor = UIColor.black.withAlphaComponent(configuration.maskAlpha)
        backgroundMaskView.alpha = 0
        containerView.addSubview(backgroundMaskView)
        
        // 添加点击手势关闭
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        backgroundMaskView.addGestureRecognizer(tapGesture)
        
        // 创建内容视图
        contentView = UIView()
        contentView.backgroundColor = AppTheme.Colors.background
        contentView.layer.cornerRadius = configuration.cornerRadius
        contentView.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        contentView.layer.shadowColor = UIColor.black.cgColor
        contentView.layer.shadowOffset = CGSize(width: 0, height: -2)
        contentView.layer.shadowRadius = 8
        contentView.layer.shadowOpacity = 0.1
        containerView.addSubview(contentView)
        
        setupHeaderView()
        setupCustomContentView()
    }

    /// 获取目标视图控制器
    /// 如果当前VC是TabBarController的直接子控制器，则返回TabBarController
    /// 否则返回当前VC
    func getTargetViewController(from presentingVC: UIViewController) -> UIViewController {
        // 检查当前VC是否是TabBarController的直接子控制器
        if let tabBarController = presentingVC.tabBarController,
           tabBarController.viewControllers?.contains(presentingVC) == true {
            print("BottomSheetManager: Using TabBarController as target")
            return tabBarController
        }

        // 检查当前VC是否是NavigationController包装的TabBar子控制器
        if let navigationController = presentingVC as? UINavigationController,
           let rootVC = navigationController.viewControllers.first,
           let tabBarController = rootVC.tabBarController,
           tabBarController.viewControllers?.contains(navigationController) == true {
            print("BottomSheetManager: Using TabBarController as target (via NavigationController)")
            return tabBarController
        }

        // 检查当前VC是否在NavigationController中，且NavigationController是TabBar的子控制器
        if let navigationController = presentingVC.navigationController,
           let tabBarController = navigationController.tabBarController,
           tabBarController.viewControllers?.contains(navigationController) == true {
            print("BottomSheetManager: Using TabBarController as target (current VC in NavigationController)")
            return tabBarController
        }

        print("BottomSheetManager: Using presenting ViewController as target")
        return presentingVC
    }
    
    func setupHeaderView() {
        print("BottomSheetManager: Setting up header view")
        // 创建头部视图
        headerView = UIView()
        headerView.backgroundColor = AppTheme.Colors.background
        contentView.addSubview(headerView)

        // 创建取消按钮
        cancelButton = UIButton(type: .system)
        cancelButton.setTitle(configuration.cancelTitle, for: .normal)
        cancelButton.setTitleColor(AppTheme.Colors.secondaryLabel, for: .normal)
        cancelButton.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        cancelButton.addTarget(self, action: #selector(cancelButtonTapped), for: .touchUpInside)
        cancelButton.backgroundColor = UIColor.red.withAlphaComponent(0.3) // 临时添加背景色用于调试
        headerView.addSubview(cancelButton)
        print("BottomSheetManager: Cancel button created and added")
        
        // 创建确定按钮
        confirmButton = UIButton(type: .system)
        confirmButton.setTitle(configuration.confirmTitle, for: .normal)
        confirmButton.setTitleColor(AppTheme.Colors.primary, for: .normal)
        confirmButton.titleLabel?.font = UIFont.boldSystemFont(ofSize: 16)
        confirmButton.addTarget(self, action: #selector(confirmButtonTapped), for: .touchUpInside)
        confirmButton.backgroundColor = UIColor.blue.withAlphaComponent(0.3) // 临时添加背景色用于调试
        headerView.addSubview(confirmButton)
        print("BottomSheetManager: Confirm button created and added")
        
        // 创建标题标签（如果需要）
        if configuration.showTitle, let title = configuration.title {
            titleLabel = UILabel()
            titleLabel.text = title
            titleLabel.textColor = AppTheme.Colors.label
            titleLabel.font = UIFont.boldSystemFont(ofSize: 17)
            titleLabel.textAlignment = .center
            headerView.addSubview(titleLabel)
        }
        
        // 添加分割线
        let separatorView = UIView()
        separatorView.backgroundColor = AppTheme.Colors.separator
        headerView.addSubview(separatorView)
        
        // 设置头部视图约束
        setupHeaderConstraints(separatorView: separatorView)
    }
    
    func setupHeaderConstraints(separatorView: UIView) {
        // 取消按钮约束
        cancelButton.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.height.equalTo(44)
            make.width.greaterThanOrEqualTo(60) // 确保按钮有最小宽度
        }

        // 确定按钮约束
        confirmButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.height.equalTo(44)
            make.width.greaterThanOrEqualTo(60) // 确保按钮有最小宽度
        }
        
        // 标题标签约束（如果存在）
        if let titleLabel = titleLabel {
            titleLabel.snp.makeConstraints { make in
                make.centerX.equalToSuperview()
                make.centerY.equalToSuperview()
                make.leading.greaterThanOrEqualTo(cancelButton.snp.trailing).offset(16)
                make.trailing.lessThanOrEqualTo(confirmButton.snp.leading).offset(-16)
            }
        }
        
        // 分割线约束
        separatorView.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(0.5)
        }
    }
    
    func setupCustomContentView() {
        guard let customView = customContentView else { return }

        contentView.addSubview(customView)

        customView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom).offset(configuration.contentInsets.top)
            make.leading.equalToSuperview().offset(configuration.contentInsets.left)
            make.trailing.equalToSuperview().offset(-configuration.contentInsets.right)
            make.bottom.equalToSuperview().offset(-configuration.contentInsets.bottom)
        }
    }

    func setupConstraints() {
        guard let presentingVC = presentingViewController else { return }

        // 获取目标视图控制器
        let targetViewController = getTargetViewController(from: presentingVC)

        // 容器视图约束
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 背景遮罩约束
        backgroundMaskView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 头部视图约束
        headerView.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(configuration.headerHeight)
        }

        // 计算内容视图的高度
        let customViewHeight = customContentView?.systemLayoutSizeFitting(
            CGSize(width: targetViewController.view.bounds.width - configuration.contentInsets.left - configuration.contentInsets.right,
                   height: UIView.layoutFittingCompressedSize.height),
            withHorizontalFittingPriority: .required,
            verticalFittingPriority: .fittingSizeLevel
        ).height ?? 200

        let totalHeight = configuration.headerHeight + customViewHeight + configuration.contentInsets.top + configuration.contentInsets.bottom
        let maxHeight = targetViewController.view.bounds.height * 0.8 // 最大高度为屏幕的80%
        let finalHeight = min(totalHeight, maxHeight)

        // 内容视图约束
        contentView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.bottom.equalToSuperview().offset(finalHeight) // 初始位置在屏幕下方
            make.height.equalTo(finalHeight)
        }
    }
}

// MARK: - 私有方法 - 动画
private extension BottomSheetManager {

    func presentWithAnimation() {
        guard let presentingVC = presentingViewController else { return }

        // 获取目标视图控制器
        let targetViewController = getTargetViewController(from: presentingVC)

        // 强制布局更新
        targetViewController.view.layoutIfNeeded()

        // 执行动画
        UIView.animate(
            withDuration: configuration.animationDuration,
            delay: 0,
            usingSpringWithDamping: 0.8,
            initialSpringVelocity: 0,
            options: [.curveEaseOut],
            animations: {
                // 显示背景遮罩
                self.backgroundMaskView.alpha = 1

                // 将内容视图移动到正确位置
                self.contentView.snp.updateConstraints { make in
                    make.bottom.equalToSuperview()
                }

                targetViewController.view.layoutIfNeeded()
            },
            completion: nil
        )
    }

    func dismissWithAnimation() {
        guard let presentingVC = presentingViewController else { return }

        // 获取目标视图控制器
        let targetViewController = getTargetViewController(from: presentingVC)

        UIView.animate(
            withDuration: configuration.animationDuration,
            delay: 0,
            options: [.curveEaseIn],
            animations: {
                // 隐藏背景遮罩
                self.backgroundMaskView.alpha = 0

                // 将内容视图移动到屏幕下方
                self.contentView.snp.updateConstraints { make in
                    make.bottom.equalToSuperview().offset(self.contentView.bounds.height)
                }

                targetViewController.view.layoutIfNeeded()
            },
            completion: { _ in
                // 移除所有视图
                self.containerView.removeFromSuperview()
                self.containerView = nil
                self.backgroundMaskView = nil
                self.contentView = nil
                self.headerView = nil
                self.customContentView = nil
                self.cancelButton = nil
                self.confirmButton = nil
                self.titleLabel = nil
                self.presentingViewController = nil

                // 从活跃管理器列表中移除，允许释放
                BottomSheetManager.activeManagers.removeAll { $0 === self }
            }
        )
    }
}

// MARK: - 私有方法 - 事件处理
private extension BottomSheetManager {

    @objc func backgroundTapped() {
        print("BottomSheetManager: backgroundTapped called")
        // 点击背景时执行取消操作
        cancelAction?()
        dismiss()
    }

    @objc func cancelButtonTapped() {
        print("BottomSheetManager: cancelButtonTapped called")
        cancelAction?()
        dismiss()
    }

    @objc func confirmButtonTapped() {
        print("BottomSheetManager: confirmButtonTapped called")
        // 如果有数据回调，先验证选择并获取数据
        if let confirmActionWithData = confirmActionWithData,
           let contentProtocol = customContentView as? BottomSheetContentProtocol {

            // 验证选择是否有效
            guard contentProtocol.validateSelection() else {
                print("BottomSheetManager: validation failed")
                // 可以在这里显示错误提示
                return
            }

            // 获取选中的数据
            let selectedData = contentProtocol.getSelectedData()
            confirmActionWithData(selectedData)
        } else {
            // 使用普通回调
            confirmAction?()
        }

        dismiss()
    }
}

// MARK: - 便捷方法
public extension BottomSheetManager {

    /// 快速显示底部半屏视图的便捷方法
    /// - Parameters:
    ///   - customView: 自定义内容视图
    ///   - from: 展示的视图控制器
    ///   - title: 标题（可选）
    ///   - cancelTitle: 取消按钮标题（可选，默认为"取消"）
    ///   - confirmTitle: 确定按钮标题（可选，默认为"确定"）
    ///   - cancelAction: 取消按钮回调
    ///   - confirmAction: 确定按钮回调
    /// - Returns: BottomSheetManager实例，用于后续操作
    @discardableResult
    static func show(
        customView: UIView,
        from viewController: UIViewController,
        title: String? = nil,
        cancelTitle: String? = nil,
        confirmTitle: String? = nil,
        cancelAction: ButtonAction? = nil,
        confirmAction: ButtonAction? = nil
    ) -> BottomSheetManager {

        var config = Configuration()
        config.title = title
        config.showTitle = title != nil

        if let cancelTitle = cancelTitle {
            config.cancelTitle = cancelTitle
        }

        if let confirmTitle = confirmTitle {
            config.confirmTitle = confirmTitle
        }

        let manager = BottomSheetManager(configuration: config)
        manager.show(
            customView: customView,
            from: viewController,
            cancelAction: cancelAction,
            confirmAction: confirmAction
        )

        return manager
    }

    /// 快速显示底部半屏视图的便捷方法（支持数据回调）
    /// - Parameters:
    ///   - customView: 自定义内容视图（需要实现BottomSheetContentProtocol协议）
    ///   - from: 展示的视图控制器
    ///   - title: 标题（可选）
    ///   - cancelTitle: 取消按钮标题（可选，默认为"取消"）
    ///   - confirmTitle: 确定按钮标题（可选，默认为"确定"）
    ///   - cancelAction: 取消按钮回调
    ///   - confirmAction: 确定按钮回调，会传入选中的数据
    /// - Returns: BottomSheetManager实例，用于后续操作
    @discardableResult
    static func showWithDataCallback(
        customView: UIView & BottomSheetContentProtocol,
        from viewController: UIViewController,
        title: String? = nil,
        cancelTitle: String? = nil,
        confirmTitle: String? = nil,
        cancelAction: ButtonAction? = nil,
        confirmAction: @escaping ConfirmActionWithData
    ) -> BottomSheetManager {

        var config = Configuration()
        config.title = title
        config.showTitle = title != nil

        if let cancelTitle = cancelTitle {
            config.cancelTitle = cancelTitle
        }

        if let confirmTitle = confirmTitle {
            config.confirmTitle = confirmTitle
        }

        let manager = BottomSheetManager(configuration: config)
        manager.showWithDataCallback(
            customView: customView,
            from: viewController,
            cancelAction: cancelAction,
            confirmAction: confirmAction
        )

        return manager
    }
}
