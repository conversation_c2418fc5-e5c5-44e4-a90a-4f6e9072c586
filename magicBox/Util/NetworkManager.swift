import Foundation
import Alamofire

/**
 NetworkManager - 网络请求管理类
 
 使用示例:
 
 1. GET请求（返回解码后的模型）:
 ```swift
 struct User: Codable {
     let id: Int
     let name: String
 }
 
 NetworkManager.shared.get("https://api.example.com/users/1",
                          responseType: User.self) { result in
     switch result {
     case .success(let user):
         print("User name: \(user.name)")
     case .failure(let error):
         print("Error: \(error)")
     }
 }
 ```
 
 2. GET请求（带参数）:
 ```swift
 let params = ["page": 1, "limit": 20]
 NetworkManager.shared.get("https://api.example.com/users",
                          parameters: params,
                          responseType: [User].self) { result in
     // 处理结果
 }
 ```
 
 3. POST请求（JSON编码）:
 ```swift
 let params = ["name": "John", "email": "<EMAIL>"]
 NetworkManager.shared.post("https://api.example.com/users",
                           parameters: params,
                           responseType: User.self) { result in
     // 处理结果
 }
 ```
 
 4. GET请求（返回原始Data）:
 ```swift
 NetworkManager.shared.get("https://api.example.com/image.png") { result in
     switch result {
     case .success(let data):
         let image = UIImage(data: data)
     case .failure(let error):
         print("Error: \(error)")
     }
 }
 ```
 
 5. 自定义Headers:
 ```swift
 let headers: HTTPHeaders = [
     "Authorization": "Bearer your-token",
     "Accept": "application/json"
 ]
 NetworkManager.shared.get("https://api.example.com/protected",
                          headers: headers,
                          responseType: User.self) { result in
     // 处理结果
 }
 ```
 
 6. 取消所有请求:
 ```swift
 NetworkManager.shared.cancelAllRequests()
 ```
 */
class NetworkManager {
    
    static let shared = NetworkManager()
    
    private init() {}
    
    private let session: Session = {
        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 30
        configuration.timeoutIntervalForResource = 30
        return Session(configuration: configuration)
    }()
    
    typealias CompletionHandler<T> = (Result<T, Error>) -> Void
    
    func get<T: Decodable>(_ url: String,
                           parameters: Parameters? = nil,
                           headers: HTTPHeaders? = nil,
                           responseType: T.Type,
                           completion: @escaping CompletionHandler<T>) {
        
        session.request(url,
                       method: .get,
                       parameters: parameters,
                       encoding: URLEncoding.default,
                       headers: headers)
            .validate()
            .responseDecodable(of: T.self) { response in
                switch response.result {
                case .success(let value):
                    completion(.success(value))
                case .failure(let error):
                    completion(.failure(error))
                }
            }
    }
    
    func get(_ url: String,
             parameters: Parameters? = nil,
             headers: HTTPHeaders? = nil,
             completion: @escaping CompletionHandler<Data>) {
        
        session.request(url,
                       method: .get,
                       parameters: parameters,
                       encoding: URLEncoding.default,
                       headers: headers)
            .validate()
            .responseData { response in
                switch response.result {
                case .success(let data):
                    completion(.success(data))
                case .failure(let error):
                    completion(.failure(error))
                }
            }
    }
    
    func post<T: Decodable>(_ url: String,
                            parameters: Parameters? = nil,
                            headers: HTTPHeaders? = nil,
                            encoding: ParameterEncoding = JSONEncoding.default,
                            responseType: T.Type,
                            completion: @escaping CompletionHandler<T>) {
        
        session.request(url,
                       method: .post,
                       parameters: parameters,
                       encoding: encoding,
                       headers: headers)
            .validate()
            .responseDecodable(of: T.self) { response in
                switch response.result {
                case .success(let value):
                    completion(.success(value))
                case .failure(let error):
                    completion(.failure(error))
                }
            }
    }
    
    func post(_ url: String,
              parameters: Parameters? = nil,
              headers: HTTPHeaders? = nil,
              encoding: ParameterEncoding = JSONEncoding.default,
              completion: @escaping CompletionHandler<Data>) {
        
        session.request(url,
                       method: .post,
                       parameters: parameters,
                       encoding: encoding,
                       headers: headers)
            .validate()
            .responseData { response in
                switch response.result {
                case .success(let data):
                    completion(.success(data))
                case .failure(let error):
                    completion(.failure(error))
                }
            }
    }
    
    func cancelAllRequests() {
        session.cancelAllRequests()
    }
}

extension NetworkManager {
    
    enum NetworkError: LocalizedError {
        case invalidURL
        case noData
        case decodingError
        case custom(String)
        
        var errorDescription: String? {
            switch self {
            case .invalidURL:
                return "Invalid URL"
            case .noData:
                return "No data received"
            case .decodingError:
                return "Failed to decode response"
            case .custom(let message):
                return message
            }
        }
    }
}