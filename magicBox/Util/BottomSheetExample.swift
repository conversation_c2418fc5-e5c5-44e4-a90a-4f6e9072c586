import UIKit
import SnapKit

/// BottomSheetManager 使用示例
class BottomSheetExample {
    
    // MARK: - 示例1：简单的文本内容
    static func showSimpleTextExample(from viewController: UIViewController) {
        // 创建自定义内容视图
        let contentView = UIView()
        contentView.backgroundColor = AppTheme.Colors.background
        
        // 添加标签
        let label = UILabel()
        label.text = "这是一个简单的文本示例\n\n您可以在这里放置任何自定义内容，\n底部半屏视图会根据内容的高度自动调整。"
        label.textColor = AppTheme.Colors.label
        label.font = UIFont.systemFont(ofSize: 16)
        label.numberOfLines = 0
        label.textAlignment = .center
        contentView.addSubview(label)
        
        label.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(20)
        }
        
        // 显示底部半屏视图
        BottomSheetManager.show(
            customView: contentView,
            from: viewController,
            title: "简单示例",
            cancelAction: {
                print("用户点击了取消")
            },
            confirmAction: {
                print("用户点击了确定")
            }
        )
    }
    
    // MARK: - 示例2：表单输入
    static func showFormExample(from viewController: UIViewController) {
        // 创建表单视图
        let formView = FormInputView()

        // 显示底部半屏视图（使用数据回调）
        BottomSheetManager.showWithDataCallback(
            customView: formView,
            from: viewController,
            title: "编辑信息",
            cancelTitle: "取消",
            confirmTitle: "保存",
            cancelAction: {
                print("取消编辑")
            },
            confirmAction: { formData in
                if let data = formData as? [String: String] {
                    print("保存表单数据: \(data)")
                    let message = "姓名: \(data["name"] ?? "")\n邮箱: \(data["email"] ?? "")\n电话: \(data["phone"] ?? "")"
                    showAlert(on: viewController, title: "保存成功", message: message)
                } else {
                    print("表单数据无效")
                    showAlert(on: viewController, title: "错误", message: "请填写完整的表单信息")
                }
            }
        )
    }
    
    // MARK: - 示例3：选择列表
    static func showSelectionExample(from viewController: UIViewController) {
        // 创建选择列表视图
        let selectionView = SelectionListView()

        // 显示底部半屏视图（使用数据回调）
        BottomSheetManager.showWithDataCallback(
            customView: selectionView,
            from: viewController,
            title: "选择选项",
            cancelAction: {
                print("取消选择")
            },
            confirmAction: { selectedData in
                if let selectedOption = selectedData as? String {
                    print("用户选择了: \(selectedOption)")
                    // 在这里处理选中的数据
                    showAlert(on: viewController, title: "选择结果", message: "您选择了: \(selectedOption)")
                } else {
                    print("没有选择任何选项")
                    showAlert(on: viewController, title: "提示", message: "请先选择一个选项")
                }
            }
        )
    }
    
    // MARK: - 示例4：自定义配置
    static func showCustomConfigExample(from viewController: UIViewController) {
        // 创建自定义配置
        var config = BottomSheetManager.Configuration()
        config.title = "自定义配置"
        config.cancelTitle = "关闭"
        config.confirmTitle = "完成"
        config.cornerRadius = 24
        config.animationDuration = 0.5
        config.contentInsets = UIEdgeInsets(top: 24, left: 24, bottom: 24, right: 24)
        
        // 创建内容视图
        let contentView = createCustomContentView()
        
        // 使用自定义配置创建管理器
        let manager = BottomSheetManager(configuration: config)
        manager.show(
            customView: contentView,
            from: viewController,
            cancelAction: {
                print("自定义取消操作")
            },
            confirmAction: {
                print("自定义确认操作")
            }
        )
    }
}

// MARK: - 私有辅助方法
private extension BottomSheetExample {

    /// 显示警告对话框的辅助方法
    static func showAlert(on viewController: UIViewController, title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        viewController.present(alert, animated: true)
    }
    
    static func createFormView() -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = AppTheme.Colors.background
        
        // 创建堆栈视图
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 16
        stackView.distribution = .fill
        containerView.addSubview(stackView)
        
        // 姓名输入框
        let nameField = createTextField(placeholder: "请输入姓名")
        stackView.addArrangedSubview(nameField)
        
        // 邮箱输入框
        let emailField = createTextField(placeholder: "请输入邮箱")
        emailField.keyboardType = .emailAddress
        stackView.addArrangedSubview(emailField)
        
        // 电话输入框
        let phoneField = createTextField(placeholder: "请输入电话")
        phoneField.keyboardType = .phonePad
        stackView.addArrangedSubview(phoneField)
        
        // 备注文本视图
        let textView = UITextView()
        textView.backgroundColor = AppTheme.Colors.secondaryGroupedBackground
        textView.layer.cornerRadius = 8
        textView.font = UIFont.systemFont(ofSize: 16)
        textView.textColor = AppTheme.Colors.label
        textView.text = "请输入备注信息..."
        stackView.addArrangedSubview(textView)
        
        textView.snp.makeConstraints { make in
            make.height.equalTo(80)
        }
        
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        return containerView
    }
    
    static func createTextField(placeholder: String) -> UITextField {
        let textField = UITextField()
        textField.placeholder = placeholder
        textField.backgroundColor = AppTheme.Colors.secondaryGroupedBackground
        textField.layer.cornerRadius = 8
        textField.font = UIFont.systemFont(ofSize: 16)
        textField.textColor = AppTheme.Colors.label
        
        // 添加左边距
        let paddingView = UIView(frame: CGRect(x: 0, y: 0, width: 12, height: 44))
        textField.leftView = paddingView
        textField.leftViewMode = .always
        
        textField.snp.makeConstraints { make in
            make.height.equalTo(44)
        }
        
        return textField
    }
    
    static func createSelectionView() -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = AppTheme.Colors.background
        
        let options = ["选项一", "选项二", "选项三", "选项四", "选项五"]
        
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 1
        stackView.distribution = .fill
        containerView.addSubview(stackView)
        
        for (index, option) in options.enumerated() {
            let button = UIButton(type: .system)

            // 使用现代的UIButton配置
            var config = UIButton.Configuration.filled()
            config.title = option
            config.baseBackgroundColor = AppTheme.Colors.secondaryGroupedBackground
            config.baseForegroundColor = AppTheme.Colors.label
            config.contentInsets = NSDirectionalEdgeInsets(top: 0, leading: 16, bottom: 0, trailing: 16)
            config.titleAlignment = .leading

            button.configuration = config
            button.tag = index

            button.snp.makeConstraints { make in
                make.height.equalTo(50)
            }

            stackView.addArrangedSubview(button)
        }
        
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        return containerView
    }
    
    static func createCustomContentView() -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = AppTheme.Colors.background
        
        // 添加图片视图
        let imageView = UIImageView()
        imageView.backgroundColor = AppTheme.Colors.secondaryGroupedBackground
        imageView.layer.cornerRadius = 12
        imageView.contentMode = .scaleAspectFit
        containerView.addSubview(imageView)
        
        // 添加描述标签
        let descriptionLabel = UILabel()
        descriptionLabel.text = "这是一个自定义配置的示例，\n展示了如何使用不同的配置参数\n来定制底部半屏视图的外观和行为。"
        descriptionLabel.textColor = AppTheme.Colors.label
        descriptionLabel.font = UIFont.systemFont(ofSize: 14)
        descriptionLabel.numberOfLines = 0
        descriptionLabel.textAlignment = .center
        containerView.addSubview(descriptionLabel)
        
        imageView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.centerX.equalToSuperview()
            make.width.height.equalTo(80)
        }
        
        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(16)
            make.leading.trailing.bottom.equalToSuperview()
        }
        
        return containerView
    }
}

// MARK: - 实现协议的示例视图

/// 表单输入视图（实现BottomSheetContentProtocol协议）
class FormInputView: UIView, BottomSheetContentProtocol {

    private var nameField: UITextField!
    private var emailField: UITextField!
    private var phoneField: UITextField!
    private var noteTextView: UITextView!

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }

    private func setupUI() {
        backgroundColor = AppTheme.Colors.background

        // 创建堆栈视图
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 16
        stackView.distribution = .fill
        addSubview(stackView)

        // 姓名输入框
        nameField = createTextField(placeholder: "请输入姓名")
        stackView.addArrangedSubview(nameField)

        // 邮箱输入框
        emailField = createTextField(placeholder: "请输入邮箱")
        emailField.keyboardType = .emailAddress
        stackView.addArrangedSubview(emailField)

        // 电话输入框
        phoneField = createTextField(placeholder: "请输入电话")
        phoneField.keyboardType = .phonePad
        stackView.addArrangedSubview(phoneField)

        // 备注文本视图
        noteTextView = UITextView()
        noteTextView.backgroundColor = AppTheme.Colors.secondaryGroupedBackground
        noteTextView.layer.cornerRadius = 8
        noteTextView.font = UIFont.systemFont(ofSize: 16)
        noteTextView.textColor = AppTheme.Colors.label
        noteTextView.text = "请输入备注信息..."
        stackView.addArrangedSubview(noteTextView)

        noteTextView.snp.makeConstraints { make in
            make.height.equalTo(80)
        }

        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    private func createTextField(placeholder: String) -> UITextField {
        let textField = UITextField()
        textField.placeholder = placeholder
        textField.backgroundColor = AppTheme.Colors.secondaryGroupedBackground
        textField.layer.cornerRadius = 8
        textField.font = UIFont.systemFont(ofSize: 16)
        textField.textColor = AppTheme.Colors.label

        // 添加左边距
        let paddingView = UIView(frame: CGRect(x: 0, y: 0, width: 12, height: 44))
        textField.leftView = paddingView
        textField.leftViewMode = .always

        textField.snp.makeConstraints { make in
            make.height.equalTo(44)
        }

        return textField
    }

    // MARK: - BottomSheetContentProtocol

    func getSelectedData() -> Any? {
        let formData: [String: String] = [
            "name": nameField.text ?? "",
            "email": emailField.text ?? "",
            "phone": phoneField.text ?? "",
            "note": noteTextView.text ?? ""
        ]
        return formData
    }

    func validateSelection() -> Bool {
        // 验证必填字段
        guard let name = nameField.text, !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return false
        }

        guard let email = emailField.text, !email.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return false
        }

        return true
    }

    func resetSelection() {
        nameField.text = ""
        emailField.text = ""
        phoneField.text = ""
        noteTextView.text = "请输入备注信息..."
    }
}

/// 选择列表视图（实现BottomSheetContentProtocol协议）
class SelectionListView: UIView, BottomSheetContentProtocol {

    private var selectedIndex: Int?
    private let options = ["选项一", "选项二", "选项三", "选项四", "选项五"]
    private var buttons: [UIButton] = []

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }

    private func setupUI() {
        backgroundColor = AppTheme.Colors.background

        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 1
        stackView.distribution = .fill
        addSubview(stackView)

        for (index, option) in options.enumerated() {
            let button = UIButton(type: .system)

            // 使用现代的UIButton配置
            var config = UIButton.Configuration.filled()
            config.title = option
            config.baseBackgroundColor = AppTheme.Colors.secondaryGroupedBackground
            config.baseForegroundColor = AppTheme.Colors.label
            config.contentInsets = NSDirectionalEdgeInsets(top: 0, leading: 16, bottom: 0, trailing: 16)
            config.titleAlignment = .leading

            button.configuration = config
            button.tag = index
            button.addTarget(self, action: #selector(optionButtonTapped(_:)), for: .touchUpInside)

            button.snp.makeConstraints { make in
                make.height.equalTo(50)
            }

            stackView.addArrangedSubview(button)
            buttons.append(button)
        }

        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    @objc private func optionButtonTapped(_ sender: UIButton) {
        let index = sender.tag

        // 重置所有按钮状态
        buttons.forEach { button in
            var config = button.configuration ?? UIButton.Configuration.filled()
            config.baseBackgroundColor = AppTheme.Colors.secondaryGroupedBackground
            config.baseForegroundColor = AppTheme.Colors.label
            button.configuration = config
        }

        // 设置选中状态
        var selectedConfig = sender.configuration ?? UIButton.Configuration.filled()
        selectedConfig.baseBackgroundColor = AppTheme.Colors.primary
        selectedConfig.baseForegroundColor = UIColor.white
        sender.configuration = selectedConfig
        selectedIndex = index
    }

    // MARK: - BottomSheetContentProtocol

    func getSelectedData() -> Any? {
        guard let selectedIndex = selectedIndex else {
            return nil
        }
        return options[selectedIndex]
    }

    func validateSelection() -> Bool {
        return selectedIndex != nil
    }

    func resetSelection() {
        selectedIndex = nil
        buttons.forEach { button in
            var config = button.configuration ?? UIButton.Configuration.filled()
            config.baseBackgroundColor = AppTheme.Colors.secondaryGroupedBackground
            config.baseForegroundColor = AppTheme.Colors.label
            button.configuration = config
        }
    }
}
