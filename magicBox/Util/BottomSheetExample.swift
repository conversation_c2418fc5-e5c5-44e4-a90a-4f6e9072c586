import UIKit
import SnapKit

/// BottomSheetManager 使用示例
class BottomSheetExample {
    
    // MARK: - 示例1：简单的文本内容
    static func showSimpleTextExample(from viewController: UIViewController) {
        // 创建自定义内容视图
        let contentView = UIView()
        contentView.backgroundColor = AppTheme.Colors.background
        
        // 添加标签
        let label = UILabel()
        label.text = "这是一个简单的文本示例\n\n您可以在这里放置任何自定义内容，\n底部半屏视图会根据内容的高度自动调整。"
        label.textColor = AppTheme.Colors.label
        label.font = UIFont.systemFont(ofSize: 16)
        label.numberOfLines = 0
        label.textAlignment = .center
        contentView.addSubview(label)
        
        label.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(20)
        }
        
        // 显示底部半屏视图
        BottomSheetManager.show(
            customView: contentView,
            from: viewController,
            title: "简单示例",
            cancelAction: {
                print("用户点击了取消")
            },
            confirmAction: {
                print("用户点击了确定")
            }
        )
    }
    
    // MARK: - 示例2：表单输入
    static func showFormExample(from viewController: UIViewController) {
        // 创建表单视图
        let formView = createFormView()
        
        // 显示底部半屏视图
        BottomSheetManager.show(
            customView: formView,
            from: viewController,
            title: "编辑信息",
            cancelTitle: "取消",
            confirmTitle: "保存",
            cancelAction: {
                print("取消编辑")
            },
            confirmAction: {
                print("保存信息")
                // 这里可以获取表单数据并保存
            }
        )
    }
    
    // MARK: - 示例3：选择列表
    static func showSelectionExample(from viewController: UIViewController) {
        // 创建选择列表视图
        let selectionView = createSelectionView()
        
        // 显示底部半屏视图
        BottomSheetManager.show(
            customView: selectionView,
            from: viewController,
            title: "选择选项",
            cancelAction: {
                print("取消选择")
            },
            confirmAction: {
                print("确认选择")
            }
        )
    }
    
    // MARK: - 示例4：自定义配置
    static func showCustomConfigExample(from viewController: UIViewController) {
        // 创建自定义配置
        var config = BottomSheetManager.Configuration()
        config.title = "自定义配置"
        config.cancelTitle = "关闭"
        config.confirmTitle = "完成"
        config.cornerRadius = 24
        config.maskAlpha = 0.7
        config.animationDuration = 0.5
        config.contentInsets = UIEdgeInsets(top: 24, left: 24, bottom: 24, right: 24)
        
        // 创建内容视图
        let contentView = createCustomContentView()
        
        // 使用自定义配置创建管理器
        let manager = BottomSheetManager(configuration: config)
        manager.show(
            customView: contentView,
            from: viewController,
            cancelAction: {
                print("自定义取消操作")
            },
            confirmAction: {
                print("自定义确认操作")
            }
        )
    }
}

// MARK: - 私有辅助方法
private extension BottomSheetExample {
    
    static func createFormView() -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = AppTheme.Colors.background
        
        // 创建堆栈视图
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 16
        stackView.distribution = .fill
        containerView.addSubview(stackView)
        
        // 姓名输入框
        let nameField = createTextField(placeholder: "请输入姓名")
        stackView.addArrangedSubview(nameField)
        
        // 邮箱输入框
        let emailField = createTextField(placeholder: "请输入邮箱")
        emailField.keyboardType = .emailAddress
        stackView.addArrangedSubview(emailField)
        
        // 电话输入框
        let phoneField = createTextField(placeholder: "请输入电话")
        phoneField.keyboardType = .phonePad
        stackView.addArrangedSubview(phoneField)
        
        // 备注文本视图
        let textView = UITextView()
        textView.backgroundColor = AppTheme.Colors.secondaryGroupedBackground
        textView.layer.cornerRadius = 8
        textView.font = UIFont.systemFont(ofSize: 16)
        textView.textColor = AppTheme.Colors.label
        textView.text = "请输入备注信息..."
        stackView.addArrangedSubview(textView)
        
        textView.snp.makeConstraints { make in
            make.height.equalTo(80)
        }
        
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        return containerView
    }
    
    static func createTextField(placeholder: String) -> UITextField {
        let textField = UITextField()
        textField.placeholder = placeholder
        textField.backgroundColor = AppTheme.Colors.secondaryGroupedBackground
        textField.layer.cornerRadius = 8
        textField.font = UIFont.systemFont(ofSize: 16)
        textField.textColor = AppTheme.Colors.label
        
        // 添加左边距
        let paddingView = UIView(frame: CGRect(x: 0, y: 0, width: 12, height: 44))
        textField.leftView = paddingView
        textField.leftViewMode = .always
        
        textField.snp.makeConstraints { make in
            make.height.equalTo(44)
        }
        
        return textField
    }
    
    static func createSelectionView() -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = AppTheme.Colors.background
        
        let options = ["选项一", "选项二", "选项三", "选项四", "选项五"]
        
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 1
        stackView.distribution = .fill
        containerView.addSubview(stackView)
        
        for (index, option) in options.enumerated() {
            let button = UIButton(type: .system)
            button.setTitle(option, for: .normal)
            button.setTitleColor(AppTheme.Colors.label, for: .normal)
            button.backgroundColor = AppTheme.Colors.secondaryGroupedBackground
            button.titleLabel?.font = UIFont.systemFont(ofSize: 16)
            button.contentHorizontalAlignment = .left
            button.contentEdgeInsets = UIEdgeInsets(top: 0, left: 16, bottom: 0, right: 16)
            button.tag = index
            
            button.snp.makeConstraints { make in
                make.height.equalTo(50)
            }
            
            stackView.addArrangedSubview(button)
        }
        
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        return containerView
    }
    
    static func createCustomContentView() -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = AppTheme.Colors.background
        
        // 添加图片视图
        let imageView = UIImageView()
        imageView.backgroundColor = AppTheme.Colors.secondaryGroupedBackground
        imageView.layer.cornerRadius = 12
        imageView.contentMode = .scaleAspectFit
        containerView.addSubview(imageView)
        
        // 添加描述标签
        let descriptionLabel = UILabel()
        descriptionLabel.text = "这是一个自定义配置的示例，\n展示了如何使用不同的配置参数\n来定制底部半屏视图的外观和行为。"
        descriptionLabel.textColor = AppTheme.Colors.label
        descriptionLabel.font = UIFont.systemFont(ofSize: 14)
        descriptionLabel.numberOfLines = 0
        descriptionLabel.textAlignment = .center
        containerView.addSubview(descriptionLabel)
        
        imageView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.centerX.equalToSuperview()
            make.width.height.equalTo(80)
        }
        
        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(16)
            make.leading.trailing.bottom.equalToSuperview()
        }
        
        return containerView
    }
}
