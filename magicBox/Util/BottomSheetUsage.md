# BottomSheetManager 使用指南

## 概述

`BottomSheetManager` 是一个用于显示从底部弹出的半屏视图的工具类。它提供了简单易用的接口，支持自定义内容视图，并会根据内容高度自动调整半屏视图的大小。

## 主要特性

- 🎨 **自定义内容**: 支持传入任何自定义 UIView 作为内容
- 📏 **自适应高度**: 根据内容视图的高度自动调整半屏视图高度
- 🎭 **丰富配置**: 支持自定义标题、按钮文本、动画时长等
- 🎯 **简单易用**: 提供便捷的静态方法，一行代码即可调用
- 🎪 **优雅动画**: 内置流畅的弹出和收起动画效果

## 基本使用

### 1. 最简单的使用方式

```swift
// 创建自定义内容视图
let contentView = UIView()
// ... 设置内容视图 ...

// 显示底部半屏视图
BottomSheetManager.show(
    customView: contentView,
    from: self,
    title: "标题",
    cancelAction: {
        print("用户点击了取消")
    },
    confirmAction: {
        print("用户点击了确定")
    }
)
```

### 2. 使用自定义配置

```swift
// 创建自定义配置
var config = BottomSheetManager.Configuration()
config.title = "自定义标题"
config.cancelTitle = "关闭"
config.confirmTitle = "完成"
config.cornerRadius = 24
config.maskAlpha = 0.7
config.animationDuration = 0.5

// 创建管理器实例
let manager = BottomSheetManager(configuration: config)
manager.show(
    customView: contentView,
    from: self,
    cancelAction: { /* 取消操作 */ },
    confirmAction: { /* 确认操作 */ }
)
```

## 配置选项

### Configuration 结构体

```swift
public struct Configuration {
    /// 取消按钮标题
    public var cancelTitle: String = "取消"
    
    /// 确定按钮标题
    public var confirmTitle: String = "确定"
    
    /// 标题文本
    public var title: String?
    
    /// 是否显示标题
    public var showTitle: Bool = true
    
    /// 头部视图高度
    public var headerHeight: CGFloat = 60
    
    /// 圆角半径
    public var cornerRadius: CGFloat = 16
    
    /// 背景遮罩透明度
    public var maskAlpha: CGFloat = 0.5
    
    /// 动画时长
    public var animationDuration: TimeInterval = 0.3
    
    /// 内容视图边距
    public var contentInsets: UIEdgeInsets = UIEdgeInsets(top: 16, left: 16, bottom: 16, right: 16)
}
```

## 使用示例

### 示例1：文本内容

```swift
BottomSheetExample.showSimpleTextExample(from: self)
```

### 示例2：表单输入

```swift
BottomSheetExample.showFormExample(from: self)
```

### 示例3：选择列表

```swift
BottomSheetExample.showSelectionExample(from: self)
```

### 示例4：自定义配置

```swift
BottomSheetExample.showCustomConfigExample(from: self)
```

## 注意事项

1. **内容视图高度**: 确保传入的自定义视图能够正确计算其内在内容大小（intrinsic content size）
2. **最大高度限制**: 半屏视图的最大高度被限制为屏幕高度的80%
3. **内存管理**: 管理器会在视图消失后自动清理资源，无需手动管理
4. **线程安全**: 请确保在主线程中调用显示方法

## 高级用法

### 手动控制显示和隐藏

```swift
let manager = BottomSheetManager()
manager.show(customView: contentView, from: self)

// 稍后手动隐藏
manager.dismiss()
```

### 获取管理器实例

```swift
let manager = BottomSheetManager.show(
    customView: contentView,
    from: self,
    title: "标题"
)

// 可以保存 manager 实例用于后续操作
```

## 依赖项

- UIKit
- SnapKit (用于自动布局)
- AppTheme (项目的主题管理器)

## 兼容性

- iOS 11.0+
- Swift 5.0+
- Xcode 12.0+
