//
//  ComponentType.swift
//  magicBox
//
//  Created by xmly on 2025/7/8.
//
//  此文件包含两种组件类型定义：
//  1. ComponentType 枚举 - 全局组件类型（如颜色、品牌、尺码等）
//  2. BasicType 结构体 - 基础数据类型（如字符串、数字、日期等）

import Foundation

/// 组件类型枚举，用于管理所有组件的ID
enum ComponentType: String, CaseIterable {
    /// 颜色组件
    case color = "color"
    /// 品牌组件
    case brand = "brand"
    /// 尺码组件
    case size = "size"
    /// 材质组件
    case material = "material"
    /// 状态组件（新旧程度）
    case condition = "condition"
    /// 季节组件
    case season = "season"
    /// 分类组件
    case category = "category"
    
    /// 获取组件ID
    var componentId: Int64 {
        switch self {
        case .color:
            return 999
        case .brand:
            return 998
        case .size:
            return 997
        case .material:
            return 996
        case .condition:
            return 995
        case .season:
            return 994
        case .category:
            return 993
        }
    }
    
    /// 获取组件名称（用于日志或调试）
    var name: String {
        switch self {
        case .color:
            return "Color"
        case .brand:
            return "Brand"
        case .size:
            return "Size"
        case .material:
            return "Material"
        case .condition:
            return "Condition"
        case .season:
            return "Season"
        case .category:
            return "Category"
        }
    }
    
    /// 获取组件本地化标题
    var localizedTitle: String {
        switch self {
        case .color:
            return NSLocalizedString("component.color.title", comment: "Colors")
        case .brand:
            return NSLocalizedString("component.brand.title", comment: "Brands")
        case .size:
            return NSLocalizedString("component.size.title", comment: "Sizes")
        case .material:
            return NSLocalizedString("component.material.title", comment: "Materials")
        case .condition:
            return NSLocalizedString("component.condition.title", comment: "Condition")
        case .season:
            return NSLocalizedString("component.season.title", comment: "Season")
        case .category:
            return NSLocalizedString("component.category.title", comment: "Category")
        }
    }
    
    /// 从字符串初始化（用于数据库存储的字符串转换）
    static func from(string: String) -> ComponentType? {
        return ComponentType(rawValue: string)
    }
    
    /// 获取所有组件类型的字符串数组
    static var allRawValues: [String] {
        return allCases.map { $0.rawValue }
    }
}

// MARK: - 基础组件类型常量
extension ComponentType {
    /// 基础组件类型常量（用于 template_components 表的 component_type 字段）
    struct BasicType {
        /// 字符串类型
        static let string = "string"
        /// 数字类型
        static let number = "number"
        /// 日期类型
        static let date = "date"
        /// 布尔类型
        static let boolean = "boolean"
        /// 文本类型（长文本）
        static let text = "text"
        /// 选择类型
        static let select = "select"
        /// 多选类型
        static let multiSelect = "multiSelect"
    }
    
    /// 获取所有基础组件类型
    static var allBasicTypes: [String] {
        return [
            BasicType.string,
            BasicType.number,
            BasicType.date,
            BasicType.boolean,
            BasicType.text,
            BasicType.select,
            BasicType.multiSelect
        ]
    }
}

// MARK: - 类型判断辅助方法
extension ComponentType {
    /// 判断是否为选择类型（单选或多选）
    static func isSelectType(_ type: String) -> Bool {
        return type == BasicType.select || type == BasicType.multiSelect
    }
    
    /// 判断是否为文本输入类型
    static func isTextInputType(_ type: String) -> Bool {
        return type == BasicType.string || type == BasicType.text
    }
    
    /// 判断是否为数值类型
    static func isNumericType(_ type: String) -> Bool {
        return type == BasicType.number
    }
} 