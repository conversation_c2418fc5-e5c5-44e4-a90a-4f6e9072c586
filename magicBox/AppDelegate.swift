//
//  AppDelegate.swift
//  magicBox
//
//  Created by <PERSON><PERSON><PERSON> <PERSON>an on 2025/5/18.
//

import UIKit
import IQKeyboardManagerSwift

@main
class AppDelegate: UIResponder, UIApplicationDelegate {

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> <PERSON><PERSON> {
        
        // 配置日志工具
        configureLogger()
        
        // 配置UI工具
        configureUI()
        
        // 初始化数据库
        initializeDatabase()
        
        return true
    }
    
    // MARK: - UI配置
    private func configureUI() {
        Logger.ui.enter()
        Logger.ui.info("🎨 开始配置UI工具...")
        
        // 配置IQKeyboardManager
        IQKeyboardManager.shared.isEnabled = true
        
        Logger.ui.info("✅ UI工具配置完成")
        Logger.ui.exit()
    }
    
    // MARK: - 日志配置
    private func configureLogger() {
        // 配置日志输出选项
        Logger.configure(
            isEnabled: true,
            minimumLogLevel: .debug,
            showTimestamp: true,
            showFileInfo: false,
            showFunctionName: false,
            globalPrefix: "MagicBox",
            showGlobalPrefix: false
        )
        
        Logger.info("🚀 应用启动，日志系统已初始化", prefix: "AppDelegate")
        Logger.printConfiguration()
    }
    
    // MARK: - 数据库初始化
    private func initializeDatabase() {
        Logger.database.enter() // 使用数据库专用logger
        Logger.database.info("🗄️ 开始初始化数据库...")
        
        // 获取数据库管理器单例（这会触发数据库连接和表创建）
        let dbManager = DatabaseManager.shared
        
        // 检查数据库连接状态
        if dbManager.isConnected {
            Logger.database.info("✅ 数据库连接成功")
            
            // 打印数据库信息
            let dbInfo = dbManager.getDatabaseInfo()
            Logger.database.separator("数据库信息")
            for (key, value) in dbInfo {
                Logger.database.info("📊 \(key): \(value)")
            }
            Logger.database.separator()
            
            // 初始化预设数据（颜色、品牌、尺码等全局选项）
            initializePresetData()
            
            // 调试：打印数据库中的所有组件选项
            #if DEBUG
            DebugHelper.printAllComponentOptions()
            #endif
            
        } else {
            Logger.database.error("❌ 数据库连接失败")
            // 这里可以添加错误处理逻辑，比如显示错误提示给用户
            showDatabaseErrorAlert()
        }
        
        Logger.database.exit() // 记录函数退出
    }
    
    
    // MARK: - 初始化预设数据
    private func initializePresetData() {
        Logger.database.enter()
        
        // 检查是否已经初始化过预设数据
        let hasInitializedKey = "hasInitializedPresetData_v1" // 使用版本号便于后续更新
        let hasInitialized = UserDefaults.standard.bool(forKey: hasInitializedKey)
        
        Logger.database.info("📊 UserDefaults 标记状态: \(hasInitialized)")
        
        // 检查数据库中是否已有数据（处理数据库从其他设备同步的情况）
        var hasExistingData = false
        do {
            // 检查各个组件类型是否有数据
            let componentTypes: [ComponentType] = [.color, .brand, .size, .material, .condition, .season, .category]
            
            for componentType in componentTypes {
                let options = try DatabaseManager.shared.getComponentOptions(componentId: componentType.componentId)
                Logger.database.info("📊 组件 \(componentType.name) 现有选项数量: \(options.count)")
                if !options.isEmpty {
                    hasExistingData = true
                    break
                }
            }
            
            if hasExistingData {
                Logger.database.info("📱 检测到数据库中已有数据，可能是从其他设备同步，跳过预设数据初始化")
                // 更新 UserDefaults 标记，避免后续重复检查
                UserDefaults.standard.set(true, forKey: hasInitializedKey)
            }
        } catch {
            Logger.database.error("❌ 检查现有数据时出错: \(error.localizedDescription)")
        }
        
        // 只有当 UserDefaults 未标记且数据库中没有数据时，才初始化预设数据
        Logger.database.info("📊 初始化条件检查 - UserDefaults标记: \(hasInitialized), 数据库有数据: \(hasExistingData)")
        
        if !hasInitialized && !hasExistingData {
            Logger.database.info("🎨 首次启动且数据库为空，初始化预设数据...")
            
            do {
                // 初始化颜色和文字预设数据
                try DataSeeder.shared.initializeDefaultData()
                
                // 标记已经初始化
                UserDefaults.standard.set(true, forKey: hasInitializedKey)
                UserDefaults.standard.synchronize() // 强制同步
                Logger.database.info("✅ 预设数据初始化成功")
                
                // 再次检查数据是否真的插入了
                #if DEBUG
                DebugHelper.checkComponentOptions(componentId: ComponentType.color.componentId)
                DebugHelper.checkComponentOptions(componentId: ComponentType.brand.componentId)
                #endif
            } catch {
                Logger.database.error("❌ 预设数据初始化失败: \(error.localizedDescription)")
                Logger.database.object(error, name: "初始化错误")
            }
        } else if hasInitialized {
            Logger.database.info("ℹ️ UserDefaults 标记显示预设数据已初始化，跳过")
        } else if hasExistingData {
            Logger.database.info("ℹ️ 数据库中已有数据，跳过预设数据初始化")
        }
        
        Logger.database.exit()
    }
    
    // MARK: - 数据库错误处理
    private func showDatabaseErrorAlert() {
        Logger.ui.enter() // 使用UI专用logger
        Logger.ui.warning("准备显示数据库错误提示")
        
        // 在主线程中显示错误提示
        DispatchQueue.main.async {
            guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                  let window = windowScene.windows.first else {
                Logger.ui.error("无法获取window来显示错误提示")
                return
            }
            
            let alert = UIAlertController(
                title: "数据库错误",
                message: "数据库初始化失败，应用可能无法正常工作。请重启应用或联系开发者。",
                preferredStyle: .alert
            )
            
            alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
                Logger.ui.info("用户点击了数据库错误提示的确定按钮")
            })
            
            window.rootViewController?.present(alert, animated: true)
            Logger.ui.info("已显示数据库错误提示")
        }
        
        Logger.ui.exit()
    }

    // MARK: UISceneSession Lifecycle

    func application(_ application: UIApplication, configurationForConnecting connectingSceneSession: UISceneSession, options: UIScene.ConnectionOptions) -> UISceneConfiguration {
        Logger.debug("配置Scene连接", prefix: "SceneDelegate")
        return UISceneConfiguration(name: "Default Configuration", sessionRole: connectingSceneSession.role)
    }

    func application(_ application: UIApplication, didDiscardSceneSessions sceneSessions: Set<UISceneSession>) {
        Logger.debug("丢弃Scene会话，数量: \(sceneSessions.count)", prefix: "SceneDelegate")
    }
}


