//
//  HomeViewController.swift
//  magicBox
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/5/18.
//

import UIKit
import SnapKit

class HomeViewController: BaseViewController {
    
    // MARK: - Properties
    private var tableView: UITableView!
    
    // 定义菜单项数据
    private let menuItems: [(String, String, UIViewController.Type?)] = [
        (NSLocalizedString("home.menu.database_example", comment: "Database Example"), "database", OptionsViewController.self),
        ("底部半屏视图示例", "rectangle.bottomthird.inset.filled", nil)
    ]

    // MARK: - BaseViewController Override
    override func handleFloatingButtonTap() {
        // 创建警告对话框
        let alert = UIAlertController(
            title: NSLocalizedString("home.add.title", comment: "Add New Item"),
            message: NSLocalizedString("home.add.message", comment: "Choose an option to add"),
            preferredStyle: .actionSheet
        )
        
        // 添加选项
        alert.addAction(UIAlertAction(
            title: NSLocalizedString("home.add.photo", comment: "Add Photo"),
            style: .default,
            handler: { [weak self] _ in
                self?.showImagePicker()
            }
        ))
        
        alert.addAction(UIAlertAction(
            title: NSLocalizedString("home.add.template", comment: "Add Template"),
            style: .default,
            handler: { [weak self] _ in
                // TODO: 处理添加模板的逻辑
                self?.showAlert(title: "提示", message: "添加模板功能即将推出")
            }
        ))
        
        alert.addAction(UIAlertAction(
            title: NSLocalizedString("home.add.item", comment: "Add Item"),
            style: .default,
            handler: { [weak self] _ in
                // TODO: 处理添加项目的逻辑
                self?.showAlert(title: "提示", message: "添加项目功能即将推出")
            }
        ))
        
        // 添加取消按钮
        alert.addAction(UIAlertAction(
            title: NSLocalizedString("common.cancel", comment: "Cancel"),
            style: .cancel
        ))
        
        // 在 iPad 上需要设置弹出位置
        if let popoverController = alert.popoverPresentationController {
            popoverController.sourceView = view
            popoverController.sourceRect = CGRect(
                x: view.bounds.width - 20,
                y: view.bounds.height - 20,
                width: 1,
                height: 1
            )
        }
        
        // 显示对话框
        present(alert, animated: true)
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }
    
    private func setupUI() {
        title = NSLocalizedString("home.title", comment: "Home")
        showsFloatingButton = true
        setupTableView()
    }
    
    private func setupTableView() {
        tableView = UITableView(frame: .zero, style: .grouped)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "MenuCell")
        
        view.addSubview(tableView)
        
        tableView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
    }
    
    private func showImagePicker() {
        ImagePickerManager.shared.showImagePicker(from: self) { [weak self] images in
            if !images.isEmpty {
                // TODO: 处理选中的图片
                print("Selected \(images.count) photos")
                // 这里可以添加处理图片的代码
            }
        }
    }

    private func showBottomSheetExamples() {
        let alert = UIAlertController(
            title: "底部半屏视图示例",
            message: "选择一个示例来查看效果",
            preferredStyle: .actionSheet
        )

        alert.addAction(UIAlertAction(
            title: "简单文本示例",
            style: .default,
            handler: { [weak self] _ in
                guard let self = self else { return }
                BottomSheetExample.showSimpleTextExample(from: self)
            }
        ))

        alert.addAction(UIAlertAction(
            title: "表单输入示例",
            style: .default,
            handler: { [weak self] _ in
                guard let self = self else { return }
                BottomSheetExample.showFormExample(from: self)
            }
        ))

        alert.addAction(UIAlertAction(
            title: "选择列表示例",
            style: .default,
            handler: { [weak self] _ in
                guard let self = self else { return }
                BottomSheetExample.showSelectionExample(from: self)
            }
        ))

        alert.addAction(UIAlertAction(
            title: "自定义配置示例",
            style: .default,
            handler: { [weak self] _ in
                guard let self = self else { return }
                BottomSheetExample.showCustomConfigExample(from: self)
            }
        ))

        alert.addAction(UIAlertAction(
            title: "取消",
            style: .cancel
        ))

        // 在 iPad 上需要设置弹出位置
        if let popoverController = alert.popoverPresentationController {
            popoverController.sourceView = view
            popoverController.sourceRect = CGRect(
                x: view.bounds.midX,
                y: view.bounds.midY,
                width: 1,
                height: 1
            )
        }

        present(alert, animated: true)
    }
}

// MARK: - UITableViewDataSource
extension HomeViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return menuItems.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "MenuCell", for: indexPath)
        let menuItem = menuItems[indexPath.row]
        
        cell.textLabel?.text = menuItem.0
        cell.imageView?.image = UIImage(systemName: menuItem.1)
        cell.accessoryType = .disclosureIndicator
        
        return cell
    }
}

// MARK: - UITableViewDelegate
extension HomeViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)

        let menuItem = menuItems[indexPath.row]

        // 检查是否是底部半屏视图示例
        if indexPath.row == 1 { // 底部半屏视图示例
            let debugVC = DebugViewController()
            navigationController?.pushViewController(debugVC, animated: true)
            return
        }

        // 处理其他菜单项
        if let viewControllerClass = menuItem.2 {
            let viewController = viewControllerClass.init()
            // 使用自定义的TabBarAwareNavigationController，会自动处理tabbar的显示隐藏
            navigationController?.pushViewController(viewController, animated: true)
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 56
    }
} 
