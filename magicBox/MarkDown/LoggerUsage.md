# Logger 日志工具类使用说明

## 概述
Logger是一个功能强大的日志输出工具类，专门为iOS应用开发设计。它在Debug模式下使用NSLog输出日志，在Release模式下自动禁用日志输出，确保生产环境的性能。

## 主要特性

### 🎯 核心功能
- ✅ 只在DEBUG模式下输出日志
- ✅ 支持多种日志级别（Debug、Info、Warning、Error、Verbose）
- ✅ 使用NSLog进行底层输出
- ✅ 自动添加时间戳、文件名、行号、函数名
- ✅ 支持格式化输出
- ✅ 提供便捷的全局函数
- ✅ 可配置的输出格式
- ✅ **支持自定义前缀**（全局前缀 + 业务前缀 + 临时前缀）

### 📊 日志级别
| 级别 | Emoji | 说明 |
|------|-------|------|
| DEBUG | 🐛 | 调试信息，用于开发时跟踪代码执行 |
| VERBOSE | 📝 | 详细信息，用于记录程序执行的细节 |
| INFO | ℹ️ | 一般信息，用于记录重要的程序状态 |
| WARNING | ⚠️ | 警告信息，表示可能存在问题但不影响程序运行 |
| ERROR | ❌ | 错误信息，表示发生了错误 |

### 🏷️ 前缀系统
Logger支持三级前缀系统，帮助您更好地组织和筛选日志：

1. **全局前缀**：应用于所有日志的通用前缀（如应用名称）
2. **业务前缀**：特定业务模块的前缀（如Database、Network等）
3. **临时前缀**：在特定调用中使用的临时前缀

前缀组合格式：`[全局前缀.业务前缀.临时前缀]`

## 使用方法

### 1. 基本日志输出
```swift
// 不带前缀
Logger.debug("这是调试信息")
Logger.info("这是一般信息")
Logger.warning("这是警告信息")
Logger.error("这是错误信息")
Logger.verbose("这是详细信息")

// 带临时前缀
Logger.debug("这是调试信息", prefix: "MyModule")
Logger.info("这是一般信息", prefix: "DataProcess")
```

### 2. 使用预定义业务Logger
```swift
// 数据库相关操作
Logger.database.info("执行数据库查询")
Logger.database.debug("查询参数: table=users, limit=10")

// 网络请求
Logger.network.info("发起网络请求")
Logger.network.debug("请求URL: https://api.example.com/users")

// UI操作
Logger.ui.info("用户点击了按钮")
Logger.ui.debug("更新UI状态")

// 设置相关
Logger.settings.info("用户修改了设置")
Logger.settings.debug("设置项: theme=dark")
```

### 3. 创建自定义业务Logger
```swift
// 创建自定义业务Logger
let orderLogger = Logger(businessPrefix: "Order")
let paymentLogger = Logger(businessPrefix: "Payment")

// 使用自定义Logger
orderLogger.info("开始处理订单")
orderLogger.debug("订单ID: ORDER-2025-001")

paymentLogger.info("开始处理支付")
paymentLogger.debug("支付金额: ¥199.00")
```

### 4. 组合前缀使用
```swift
// 业务Logger + 临时前缀
Logger.database.info("执行复杂查询", prefix: "ComplexQuery")
Logger.database.debug("查询条件: WHERE created_at > '2025-01-01'", prefix: "ComplexQuery")

// 静态方法 + 临时前缀
Logger.info("这是一个临时前缀的日志", prefix: "TempOperation")
Logger.debug("临时操作的详细信息", prefix: "TempOperation")
```

### 5. 格式化输出
```swift
let count = 5
let name = "用户名"
Logger.info("用户 %@ 有 %d 个物品", name, count, prefix: "Data")
Logger.debug("处理进度: %.2f%%", 85.67, prefix: "Progress")
```

### 6. 特殊功能
```swift
// 打印分割线
Logger.separator()
Logger.separator("数据库信息", prefix: "Database")

// 打印对象信息
let user = ["name": "张三", "age": 25]
Logger.object(user, name: "用户信息", prefix: "UserData")

// 函数进入/退出追踪
Logger.database.enter()
// ... 函数代码 ...
Logger.database.exit()
```

### 7. 全局便捷函数（仅DEBUG模式）
```swift
#if DEBUG
logDebug("使用全局debug函数", prefix: "GlobalFunc")
logInfo("使用全局info函数", prefix: "GlobalFunc")
logWarning("使用全局warning函数", prefix: "GlobalFunc")
logError("使用全局error函数", prefix: "GlobalFunc")
#endif
```

### 8. 配置日志输出
```swift
// 在AppDelegate中配置
Logger.configure(
    isEnabled: true,
    minimumLogLevel: .debug,
    showTimestamp: true,
    showFileInfo: true,
    showFunctionName: true,
    globalPrefix: "MagicBox",
    showGlobalPrefix: true
)

// 查看当前配置
Logger.printConfiguration()
```

## 输出格式示例

### 不同前缀组合的输出格式

```
// 全局前缀 + 业务前缀
[MagicBox.Database] [🐛 DEBUG] [2025-01-19 10:30:45.123] [DatabaseManager.swift:45] [getAllItems()] 获取所有物品

// 全局前缀 + 业务前缀 + 临时前缀
[MagicBox.Database.ComplexQuery] [ℹ️ INFO] [2025-01-19 10:30:45.125] [HomeViewController.swift:52] [loadData()] 执行复杂查询

// 全局前缀 + 临时前缀
[MagicBox.TempOperation] [⚠️ WARNING] [2025-01-19 10:30:45.126] [AppDelegate.swift:85] [setupData()] 这是临时操作的警告

// 仅全局前缀
[MagicBox] [❌ ERROR] [2025-01-19 10:30:45.127] [NetworkManager.swift:23] [requestData()] 网络请求失败
```

## 预定义业务Logger

| Logger | 业务前缀 | 用途 |
|--------|----------|------|
| Logger.database | Database | 数据库相关操作 |
| Logger.network | Network | 网络请求和响应 |
| Logger.ui | UI | 用户界面相关 |
| Logger.settings | Settings | 设置页面相关 |
| Logger.auth | Auth | 认证和权限相关 |
| Logger.storage | Storage | 本地存储操作 |
| Logger.analytics | Analytics | 统计分析相关 |

## 配置选项

| 选项 | 类型 | 默认值 | 说明 |
|------|------|-------|------|
| isEnabled | Bool | DEBUG模式为true | 是否启用日志输出 |
| minimumLogLevel | LogLevel | .debug | 最低日志级别 |
| showTimestamp | Bool | true | 是否显示时间戳 |
| showFileInfo | Bool | true | 是否显示文件名和行号 |
| showFunctionName | Bool | true | 是否显示函数名 |
| globalPrefix | String | "MagicBox" | 全局前缀 |
| showGlobalPrefix | Bool | true | 是否显示全局前缀 |

## 最佳实践

### 1. 使用合适的前缀
```swift
// 推荐：使用业务相关的前缀
Logger.database.info("用户登录成功")
Logger.network.debug("API请求参数: \(params)")
Logger.ui.verbose("动画开始执行")

// 避免：使用过于通用的前缀
Logger.info("操作完成", prefix: "App") // 太通用
Logger.debug("数据处理", prefix: "Process") // 不够具体
```

### 2. 在控制台中筛选日志
```bash
# 筛选数据库相关日志
cmd+f 搜索: [MagicBox.Database]

# 筛选特定功能的日志
cmd+f 搜索: [MagicBox.Database.ComplexQuery]

# 筛选错误日志
cmd+f 搜索: [❌ ERROR]

# 筛选特定文件的日志
cmd+f 搜索: [HomeViewController.swift]
```

### 3. 业务模块日志规范
```swift
class DatabaseManager {
    private let logger = Logger(businessPrefix: "Database")
    
    func getAllItems() -> [Item] {
        logger.enter()
        logger.info("开始获取所有物品")
        
        do {
            let items = try fetchItems()
            logger.info("成功获取 \(items.count) 个物品")
            logger.exit()
            return items
        } catch {
            logger.error("获取物品失败: \(error.localizedDescription)")
            logger.object(error, name: "数据库错误")
            logger.exit()
            throw error
        }
    }
}
```

### 4. 网络请求日志
```swift
class NetworkManager {
    private let logger = Logger(businessPrefix: "Network")
    
    func request(url: String) {
        logger.info("发起网络请求", prefix: "HTTP")
        logger.debug("请求URL: \(url)", prefix: "HTTP")
        
        // 网络请求逻辑
        
        logger.info("网络请求完成", prefix: "HTTP")
    }
}
```

### 5. 用户界面日志
```swift
class HomeViewController: UIViewController {
    private let logger = Logger(businessPrefix: "UI")
    
    override func viewDidLoad() {
        super.viewDidLoad()
        logger.info("HomeViewController 加载完成")
        logger.debug("设置UI组件", prefix: "Setup")
    }
    
    @IBAction func buttonTapped(_ sender: UIButton) {
        logger.info("用户点击了按钮", prefix: "UserAction")
        logger.debug("按钮标题: \(sender.title ?? "未知")", prefix: "UserAction")
    }
}
```

## 注意事项

1. **前缀命名规范**：
   - 使用PascalCase命名（如Database、Network）
   - 避免使用特殊字符和空格
   - 保持前缀简洁明了

2. **性能考虑**：
   - 在Release模式下，所有日志代码会被自动禁用
   - 前缀处理在Debug模式下的性能影响很小

3. **日志搜索**：
   - 使用方括号`[]`来精确搜索前缀
   - 可以组合多个关键词进行搜索

4. **前缀层级**：
   - 全局前缀：应用名称或项目名称
   - 业务前缀：功能模块名称
   - 临时前缀：具体操作或特殊场景

## 与现有代码集成

将现有的日志调用升级为带前缀的版本：

```swift
// 旧代码
print("用户登录成功")
Logger.info("数据库查询完成")

// 新代码
Logger.auth.info("用户登录成功")
Logger.database.info("数据库查询完成")

// 或者使用临时前缀
Logger.info("用户登录成功", prefix: "Auth")
Logger.info("数据库查询完成", prefix: "Database")
```

通过使用前缀功能，您可以更好地组织日志，在控制台中快速定位和筛选相关信息，提高调试效率！ 