//
//  DatePickerManager.swift
//  magicBox
//
//  Created by AI Assistant on 2025/7/16.
//

import UIKit

// MARK: - 日期选择器管理器

public class DatePickerManager {

    // MARK: - 单选日期选择器

    /// 显示单选日期选择器
    /// - Parameters:
    ///   - from: 展示的视图控制器
    ///   - title: 标题
    ///   - preselectedDate: 预选日期
    ///   - minimumDate: 最小可选日期
    ///   - maximumDate: 最大可选日期
    ///   - completion: 选择完成回调，返回选中的日期
    public static func showSingleDatePicker(
        from viewController: UIViewController,
        title: String = "选择日期",
        preselectedDate: Date? = nil,
        minimumDate: Date? = nil,
        maximumDate: Date? = nil,
        completion: @escaping (Date?) -> Void
    ) {
        var config = DatePickerConfiguration()
        config.selectionMode = .single
        config.minimumDate = minimumDate
        config.maximumDate = maximumDate

        if let preselectedDate = preselectedDate {
            config.preselectedDates = [preselectedDate]
            config.initialMonth = preselectedDate
        }

        let datePickerView = DatePickerView(configuration: config)

        var sheetConfig = BottomSheetManager.Configuration()
        sheetConfig.title = title
        sheetConfig.headerStyle = .standard
        sheetConfig.cancelTitle = "取消"
        sheetConfig.confirmTitle = "确定"

        let manager = BottomSheetManager(configuration: sheetConfig)
        manager.showWithDataCallback(
            customView: datePickerView,
            from: viewController,
            cancelAction: {
                completion(nil)
            },
            confirmAction: { data in
                if let dates = data as? [Date], let selectedDate = dates.first {
                    completion(selectedDate)
                } else {
                    completion(nil)
                }
            }
        )
    }

    /// 显示多选日期选择器
    /// - Parameters:
    ///   - from: 展示的视图控制器
    ///   - title: 标题
    ///   - preselectedDates: 预选日期数组
    ///   - minimumDate: 最小可选日期
    ///   - maximumDate: 最大可选日期
    ///   - completion: 选择完成回调，返回选中的日期数组
    public static func showMultipleDatePicker(
        from viewController: UIViewController,
        title: String = "选择多个日期",
        preselectedDates: [Date] = [],
        minimumDate: Date? = nil,
        maximumDate: Date? = nil,
        completion: @escaping ([Date]?) -> Void
    ) {
        var config = DatePickerConfiguration()
        config.selectionMode = .multiple
        config.preselectedDates = Set(preselectedDates)
        config.minimumDate = minimumDate
        config.maximumDate = maximumDate

        if let firstDate = preselectedDates.first {
            config.initialMonth = firstDate
        }

        let datePickerView = DatePickerView(configuration: config)

        var sheetConfig = BottomSheetManager.Configuration()
        sheetConfig.title = title
        sheetConfig.headerStyle = .standard
        sheetConfig.cancelTitle = "取消"
        sheetConfig.confirmTitle = "确定"

        let manager = BottomSheetManager(configuration: sheetConfig)
        manager.showWithDataCallback(
            customView: datePickerView,
            from: viewController,
            cancelAction: {
                completion(nil)
            },
            confirmAction: { data in
                if let dates = data as? [Date] {
                    completion(dates)
                } else {
                    completion(nil)
                }
            }
        )
    }

    // MARK: - 便捷方法

    /// 显示生日选择器（限制最大日期为今天）
    /// - Parameters:
    ///   - from: 展示的视图控制器
    ///   - title: 标题
    ///   - preselectedDate: 预选生日
    ///   - completion: 选择完成回调
    public static func showBirthdayPicker(
        from viewController: UIViewController,
        title: String = "选择生日",
        preselectedDate: Date? = nil,
        completion: @escaping (Date?) -> Void
    ) {
        let calendar = Calendar.current
        let minimumDate = calendar.date(byAdding: .year, value: -120, to: Date()) // 120年前
        let maximumDate = Date() // 今天

        showSingleDatePicker(
            from: viewController,
            title: title,
            preselectedDate: preselectedDate,
            minimumDate: minimumDate,
            maximumDate: maximumDate,
            completion: completion
        )
    }

    /// 显示未来日期选择器（限制最小日期为今天）
    /// - Parameters:
    ///   - from: 展示的视图控制器
    ///   - title: 标题
    ///   - preselectedDate: 预选日期
    ///   - maxDaysFromNow: 从今天开始最多可选择多少天后的日期
    ///   - completion: 选择完成回调
    public static func showFutureDatePicker(
        from viewController: UIViewController,
        title: String = "选择未来日期",
        preselectedDate: Date? = nil,
        maxDaysFromNow: Int = 365,
        completion: @escaping (Date?) -> Void
    ) {
        let calendar = Calendar.current
        let minimumDate = Date() // 今天
        let maximumDate = calendar.date(byAdding: .day, value: maxDaysFromNow, to: Date())

        showSingleDatePicker(
            from: viewController,
            title: title,
            preselectedDate: preselectedDate,
            minimumDate: minimumDate,
            maximumDate: maximumDate,
            completion: completion
        )
    }

    /// 显示日期范围选择器（选择开始和结束日期）
    /// - Parameters:
    ///   - from: 展示的视图控制器
    ///   - title: 标题
    ///   - startDate: 预选开始日期
    ///   - endDate: 预选结束日期
    ///   - minimumDate: 最小可选日期
    ///   - maximumDate: 最大可选日期
    ///   - completion: 选择完成回调，返回开始和结束日期
    public static func showDateRangePicker(
        from viewController: UIViewController,
        title: String = "选择日期范围",
        startDate: Date? = nil,
        endDate: Date? = nil,
        minimumDate: Date? = nil,
        maximumDate: Date? = nil,
        completion: @escaping (Date?, Date?) -> Void
    ) {
        var preselectedDates: [Date] = []
        if let startDate = startDate {
            preselectedDates.append(startDate)
        }
        if let endDate = endDate {
            preselectedDates.append(endDate)
        }

        showMultipleDatePicker(
            from: viewController,
            title: title,
            preselectedDates: preselectedDates,
            minimumDate: minimumDate,
            maximumDate: maximumDate
        ) { selectedDates in
            guard let dates = selectedDates, !dates.isEmpty else {
                completion(nil, nil)
                return
            }

            let sortedDates = dates.sorted()
            let start = sortedDates.first
            let end = sortedDates.count > 1 ? sortedDates.last : sortedDates.first

            completion(start, end)
        }
    }
}
