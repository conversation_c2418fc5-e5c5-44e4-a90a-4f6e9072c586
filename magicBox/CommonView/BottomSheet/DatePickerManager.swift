//
//  DatePickerManager.swift
//  magicBox
//
//  Created by AI Assistant on 2025/7/16.
//

import UIKit

// MARK: - 日期选择器管理器

public class DatePickerManager {

    // MARK: - 单选日期选择器

    /// 显示单选日期选择器
    /// - Parameters:
    ///   - from: 展示的视图控制器
    ///   - title: 标题
    ///   - completion: 选择完成回调，返回选中的日期
    public static func showSingleDatePicker(
        from viewController: UIViewController,
        title: String = "选择日期",
        completion: @escaping (Date?) -> Void
    ) {
        let config = DatePickerConfiguration()
        let datePickerView = DatePickerView(configuration: config)

        var sheetConfig = BottomSheetManager.Configuration()
        sheetConfig.title = title
        sheetConfig.headerStyle = .standard
        sheetConfig.cancelTitle = "取消"
        sheetConfig.confirmTitle = "确定"

        let manager = BottomSheetManager(configuration: sheetConfig)
        manager.show(
            customView: datePickerView,
            from: viewController
        )

        completion(Date()) // 临时返回当前日期
    }

}
