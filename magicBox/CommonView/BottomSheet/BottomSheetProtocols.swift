//
//  BottomSheetProtocols.swift
//  magicBox
//
//  Created by AI Assistant on 2025/7/16.
//

import UIKit

// MARK: - 内容视图协议

/// 底部半屏视图内容协议
public protocol BottomSheetContentProtocol: AnyObject {
    /// 获取用户选择的数据
    /// - Returns: 返回用户选择的数据，如果没有选择则返回nil
    func getSelectedData() -> Any?

    /// 验证当前选择是否有效（可选实现）
    /// - Returns: 如果当前选择有效返回true，否则返回false
    func validateSelection() -> Bool

    /// 重置选择状态（可选实现）
    func resetSelection()
}

/// 为协议提供默认实现
public extension BottomSheetContentProtocol {
    func validateSelection() -> Bool {
        return true // 默认总是有效
    }

    func resetSelection() {
        // 默认空实现
    }
}

// MARK: - HeaderView 协议

/// HeaderView 协议，所有HeaderView都需要遵循此协议
public protocol BottomSheetHeaderViewProtocol: UIView {
    /// HeaderView的高度
    var headerHeight: CGFloat { get }
    
    /// 设置取消按钮回调
    func setCancelAction(_ action: @escaping () -> Void)
    
    /// 设置确定按钮回调
    func setConfirmAction(_ action: @escaping () -> Void)
    
    /// 更新标题
    func updateTitle(_ title: String?)
    
    /// 更新按钮标题
    func updateButtonTitles(cancel: String?, confirm: String?)
}

// MARK: - HeaderView 样式枚举

/// HeaderView 样式枚举
public enum BottomSheetHeaderStyle {
    /// 标准样式：取消按钮 + 标题 + 确定按钮
    case standard
    /// 简洁样式：只有确定按钮
    case simple
    /// 自定义样式：只有标题和关闭按钮
    case titleOnly
    /// 无按钮样式：只有标题
    case titleWithoutButtons
    
    /// 获取对应的HeaderView类型
    func createHeaderView(configuration: BottomSheetManager.Configuration) -> BottomSheetHeaderViewProtocol {
        switch self {
        case .standard:
            return StandardHeaderView(configuration: configuration)
        case .simple:
            return SimpleHeaderView(configuration: configuration)
        case .titleOnly:
            return TitleOnlyHeaderView(configuration: configuration)
        case .titleWithoutButtons:
            return TitleWithoutButtonsHeaderView(configuration: configuration)
        }
    }
}
