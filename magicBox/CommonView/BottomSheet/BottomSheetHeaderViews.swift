//
//  BottomSheetHeaderViews.swift
//  magicBox
//
//  Created by AI Assistant on 2025/7/16.
//

import UIKit
import SnapKit

// MARK: - 标准HeaderView（取消 + 标题 + 确定）

class StandardHeaderView: UIView, BottomSheetHeaderViewProtocol {
    
    var headerHeight: CGFloat { return 60 }
    
    private var cancelButton: UIButton!
    private var confirmButton: UIButton!
    private var titleLabel: UILabel!
    private var separatorView: UIView!
    
    private var cancelAction: (() -> Void)?
    private var confirmAction: (() -> Void)?
    
    init(configuration: BottomSheetManager.Configuration) {
        super.init(frame: .zero)
        setupViews()
        setupConstraints()
        updateTitle(configuration.title)
        updateButtonTitles(cancel: configuration.cancelTitle, confirm: configuration.confirmTitle)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupViews() {
        backgroundColor = AppTheme.Colors.background
        
        // 创建取消按钮
        cancelButton = UIButton(type: .system)
        cancelButton.setTitleColor(AppTheme.Colors.secondaryLabel, for: .normal)
        cancelButton.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        cancelButton.addTarget(self, action: #selector(cancelButtonTapped), for: .touchUpInside)
        addSubview(cancelButton)
        
        // 创建确定按钮
        confirmButton = UIButton(type: .system)
        confirmButton.setTitleColor(AppTheme.Colors.primary, for: .normal)
        confirmButton.titleLabel?.font = UIFont.boldSystemFont(ofSize: 16)
        confirmButton.addTarget(self, action: #selector(confirmButtonTapped), for: .touchUpInside)
        addSubview(confirmButton)
        
        // 创建标题标签
        titleLabel = UILabel()
        titleLabel.textColor = AppTheme.Colors.label
        titleLabel.font = UIFont.boldSystemFont(ofSize: 17)
        titleLabel.textAlignment = .center
        addSubview(titleLabel)
        
        // 添加分割线
        separatorView = UIView()
        separatorView.backgroundColor = AppTheme.Colors.separator
        addSubview(separatorView)
    }
    
    private func setupConstraints() {
        // 取消按钮约束
        cancelButton.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.height.equalTo(44)
            make.width.greaterThanOrEqualTo(60)
        }
        
        // 确定按钮约束
        confirmButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.height.equalTo(44)
            make.width.greaterThanOrEqualTo(60)
        }
        
        // 标题标签约束
        titleLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.leading.greaterThanOrEqualTo(cancelButton.snp.trailing).offset(16)
            make.trailing.lessThanOrEqualTo(confirmButton.snp.leading).offset(-16)
        }
        
        // 分割线约束
        separatorView.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(0.5)
        }
    }
    
    // MARK: - BottomSheetHeaderViewProtocol
    
    func setCancelAction(_ action: @escaping () -> Void) {
        cancelAction = action
    }
    
    func setConfirmAction(_ action: @escaping () -> Void) {
        confirmAction = action
    }
    
    func updateTitle(_ title: String?) {
        titleLabel.text = title
        titleLabel.isHidden = title == nil
    }
    
    func updateButtonTitles(cancel: String?, confirm: String?) {
        if let cancel = cancel {
            cancelButton.setTitle(cancel, for: .normal)
        }
        if let confirm = confirm {
            confirmButton.setTitle(confirm, for: .normal)
        }
    }
    
    // MARK: - Actions
    
    @objc private func cancelButtonTapped() {
        cancelAction?()
    }
    
    @objc private func confirmButtonTapped() {
        confirmAction?()
    }
}

// MARK: - 简洁HeaderView（只有确定按钮）

class SimpleHeaderView: UIView, BottomSheetHeaderViewProtocol {
    
    var headerHeight: CGFloat { return 50 }
    
    private var confirmButton: UIButton!
    private var titleLabel: UILabel!
    private var separatorView: UIView!
    
    private var cancelAction: (() -> Void)?
    private var confirmAction: (() -> Void)?
    
    init(configuration: BottomSheetManager.Configuration) {
        super.init(frame: .zero)
        setupViews()
        setupConstraints()
        updateTitle(configuration.title)
        updateButtonTitles(cancel: configuration.cancelTitle, confirm: configuration.confirmTitle)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupViews() {
        backgroundColor = AppTheme.Colors.background
        
        // 创建确定按钮
        confirmButton = UIButton(type: .system)
        confirmButton.setTitleColor(AppTheme.Colors.primary, for: .normal)
        confirmButton.titleLabel?.font = UIFont.boldSystemFont(ofSize: 16)
        confirmButton.addTarget(self, action: #selector(confirmButtonTapped), for: .touchUpInside)
        addSubview(confirmButton)
        
        // 创建标题标签
        titleLabel = UILabel()
        titleLabel.textColor = AppTheme.Colors.label
        titleLabel.font = UIFont.boldSystemFont(ofSize: 17)
        titleLabel.textAlignment = .center
        addSubview(titleLabel)
        
        // 添加分割线
        separatorView = UIView()
        separatorView.backgroundColor = AppTheme.Colors.separator
        addSubview(separatorView)
    }
    
    private func setupConstraints() {
        // 确定按钮约束
        confirmButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.height.equalTo(44)
            make.width.greaterThanOrEqualTo(60)
        }
        
        // 标题标签约束
        titleLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.trailing.lessThanOrEqualTo(confirmButton.snp.leading).offset(-16)
        }
        
        // 分割线约束
        separatorView.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(0.5)
        }
    }
    
    // MARK: - BottomSheetHeaderViewProtocol
    
    func setCancelAction(_ action: @escaping () -> Void) {
        cancelAction = action
    }
    
    func setConfirmAction(_ action: @escaping () -> Void) {
        confirmAction = action
    }
    
    func updateTitle(_ title: String?) {
        titleLabel.text = title
        titleLabel.isHidden = title == nil
    }
    
    func updateButtonTitles(cancel: String?, confirm: String?) {
        if let confirm = confirm {
            confirmButton.setTitle(confirm, for: .normal)
        }
    }
    
    // MARK: - Actions
    
    @objc private func confirmButtonTapped() {
        confirmAction?()
    }
}

// MARK: - 标题+关闭HeaderView（标题 + 关闭按钮）

class TitleOnlyHeaderView: UIView, BottomSheetHeaderViewProtocol {

    var headerHeight: CGFloat { return 50 }

    private var closeButton: UIButton!
    private var titleLabel: UILabel!
    private var separatorView: UIView!

    private var cancelAction: (() -> Void)?
    private var confirmAction: (() -> Void)?

    init(configuration: BottomSheetManager.Configuration) {
        super.init(frame: .zero)
        setupViews()
        setupConstraints()
        updateTitle(configuration.title)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupViews() {
        backgroundColor = AppTheme.Colors.background

        // 创建关闭按钮
        closeButton = UIButton(type: .system)
        closeButton.setImage(UIImage(systemName: "xmark"), for: .normal)
        closeButton.setTitleColor(AppTheme.Colors.secondaryLabel, for: .normal)
        closeButton.addTarget(self, action: #selector(closeButtonTapped), for: .touchUpInside)
        addSubview(closeButton)

        // 创建标题标签
        titleLabel = UILabel()
        titleLabel.textColor = AppTheme.Colors.label
        titleLabel.font = UIFont.boldSystemFont(ofSize: 17)
        titleLabel.textAlignment = .center
        addSubview(titleLabel)

        // 添加分割线
        separatorView = UIView()
        separatorView.backgroundColor = AppTheme.Colors.separator
        addSubview(separatorView)
    }

    private func setupConstraints() {
        // 关闭按钮约束
        closeButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(30)
        }

        // 标题标签约束
        titleLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.trailing.lessThanOrEqualTo(closeButton.snp.leading).offset(-16)
        }

        // 分割线约束
        separatorView.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(0.5)
        }
    }

    // MARK: - BottomSheetHeaderViewProtocol

    func setCancelAction(_ action: @escaping () -> Void) {
        cancelAction = action
    }

    func setConfirmAction(_ action: @escaping () -> Void) {
        confirmAction = action
    }

    func updateTitle(_ title: String?) {
        titleLabel.text = title
        titleLabel.isHidden = title == nil
    }

    func updateButtonTitles(cancel: String?, confirm: String?) {
        // 此样式不使用按钮标题
    }

    // MARK: - Actions

    @objc private func closeButtonTapped() {
        cancelAction?()
    }
}

// MARK: - 纯标题HeaderView（只有标题，无按钮）

class TitleWithoutButtonsHeaderView: UIView, BottomSheetHeaderViewProtocol {

    var headerHeight: CGFloat { return 40 }

    private var titleLabel: UILabel!
    private var separatorView: UIView!

    private var cancelAction: (() -> Void)?
    private var confirmAction: (() -> Void)?

    init(configuration: BottomSheetManager.Configuration) {
        super.init(frame: .zero)
        setupViews()
        setupConstraints()
        updateTitle(configuration.title)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupViews() {
        backgroundColor = AppTheme.Colors.background

        // 创建标题标签
        titleLabel = UILabel()
        titleLabel.textColor = AppTheme.Colors.label
        titleLabel.font = UIFont.boldSystemFont(ofSize: 17)
        titleLabel.textAlignment = .center
        addSubview(titleLabel)

        // 添加分割线
        separatorView = UIView()
        separatorView.backgroundColor = AppTheme.Colors.separator
        addSubview(separatorView)
    }

    private func setupConstraints() {
        // 标题标签约束
        titleLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
        }

        // 分割线约束
        separatorView.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(0.5)
        }
    }

    // MARK: - BottomSheetHeaderViewProtocol

    func setCancelAction(_ action: @escaping () -> Void) {
        cancelAction = action
    }

    func setConfirmAction(_ action: @escaping () -> Void) {
        confirmAction = action
    }

    func updateTitle(_ title: String?) {
        titleLabel.text = title
        titleLabel.isHidden = title == nil
    }

    func updateButtonTitles(cancel: String?, confirm: String?) {
        // 此样式不使用按钮
    }
}
