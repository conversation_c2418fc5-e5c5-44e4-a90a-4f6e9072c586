# BottomSheet 底部半屏视图组件

一个功能强大、高度可定制的iOS底部半屏视图组件，支持多种HeaderView样式和智能TabBar覆盖功能。

## 📋 目录

- [功能特性](#功能特性)
- [HeaderView样式](#headerView样式)
- [TabBar覆盖功能](#tabbar覆盖功能)
- [基础使用](#基础使用)
- [高级配置](#高级配置)
- [自定义内容视图](#自定义内容视图)
- [示例代码](#示例代码)

## ✨ 功能特性

- 🎨 **多种HeaderView样式**: 提供4种预设HeaderView样式，满足不同场景需求
- 🎪 **优雅动画**: 内置流畅的弹出和收起动画效果，与系统UIAlertController保持一致
- 📊 **数据回调**: 支持协议模式，可获取内容视图的选择数据
- ✅ **数据验证**: 内置选择验证机制，确保数据有效性
- 📱 **智能层级**: 自动检测TabBar层级，在一级VC中显示时覆盖TabBar
- 🔧 **高度可定制**: 支持自定义圆角、阴影、动画时长等
- 🎯 **易于扩展**: 基于协议设计，方便添加新的HeaderView样式

## 🎨 HeaderView样式

### 1. Standard 标准样式 (60px)
```
[取消]     标题     [确定]
```
- **适用场景**: 需要用户确认或取消的操作
- **包含元素**: 取消按钮 + 标题 + 确定按钮

### 2. Simple 简洁样式 (50px)
```
标题              [确定]
```
- **适用场景**: 简单的信息展示，只需确认
- **包含元素**: 标题 + 确定按钮

### 3. TitleOnly 标题+关闭样式 (50px)
```
标题               [×]
```
- **适用场景**: 信息展示类，用户只需关闭
- **包含元素**: 标题 + 关闭按钮(X图标)

### 4. TitleWithoutButtons 纯标题样式 (40px)
```
        标题
```
- **适用场景**: 纯展示类，无需用户操作
- **包含元素**: 仅标题

## 📱 TabBar覆盖功能

BottomSheetManager 具有智能的视图层级检测功能：

### 自动检测逻辑

1. **一级ViewController**: 当在TabBarController的直接子控制器中显示时，半屏视图会添加到TabBarController上，从而覆盖TabBar
2. **NavigationController包装**: 支持NavigationController包装的TabBar子控制器
3. **深层嵌套**: 在其他情况下，视图会添加到当前ViewController上

### 检测场景

```swift
// 场景1: 直接的TabBar子控制器
TabBarController
├── HomeViewController (一级VC) ← 在这里显示会覆盖TabBar
└── SettingsViewController (一级VC) ← 在这里显示会覆盖TabBar

// 场景2: NavigationController包装的TabBar子控制器
TabBarController
├── NavigationController
│   └── HomeViewController (一级VC) ← 在这里显示会覆盖TabBar
└── NavigationController
    └── SettingsViewController (一级VC) ← 在这里显示会覆盖TabBar

// 场景3: 深层嵌套的ViewController
TabBarController
├── NavigationController
│   ├── HomeViewController
│   └── DetailViewController ← 在这里显示不会覆盖TabBar
```

### 调试信息

在控制台中可以看到检测结果：
- `BottomSheetManager: Using TabBarController as target` - 将覆盖TabBar
- `BottomSheetManager: Using presenting ViewController as target` - 不会覆盖TabBar

## 🚀 基础使用

### 1. 简单文本展示

```swift
// 创建配置
var config = BottomSheetManager.Configuration()
config.title = "提示信息"
config.headerStyle = .simple

// 创建内容视图
let contentView = UIView()
let label = UILabel()
label.text = "这是一个简单的文本展示"
label.textAlignment = .center
contentView.addSubview(label)

// 设置约束
label.snp.makeConstraints { make in
    make.center.equalToSuperview()
    make.leading.trailing.equalToSuperview().inset(20)
}

contentView.snp.makeConstraints { make in
    make.height.equalTo(150)
}

// 显示底部半屏视图
let manager = BottomSheetManager(configuration: config)
manager.show(
    customView: contentView,
    from: self,
    confirmAction: {
        print("用户点击了确定")
    }
)
```

### 2. 带数据回调的使用

```swift
// 创建配置
var config = BottomSheetManager.Configuration()
config.title = "选择选项"
config.headerStyle = .standard

// 创建自定义内容视图（实现BottomSheetContentProtocol协议）
let selectionView = CustomSelectionView()

// 显示底部半屏视图
let manager = BottomSheetManager(configuration: config)
manager.show(
    customView: selectionView,
    from: self,
    cancelAction: {
        print("用户取消了操作")
    },
    confirmActionWithData: { data in
        if let selectedData = data {
            print("用户选择了: \(selectedData)")
        }
    }
)
```

## ⚙️ 高级配置

### 配置选项

```swift
var config = BottomSheetManager.Configuration()

// HeaderView样式
config.headerStyle = .standard  // .standard, .simple, .titleOnly, .titleWithoutButtons

// 标题和按钮文本
config.title = "自定义标题"
config.cancelTitle = "取消"
config.confirmTitle = "确定"
config.showTitle = true

// 外观配置
config.cornerRadius = 16.0
config.shadowOpacity = 0.1
config.shadowRadius = 8.0
config.shadowOffset = CGSize(width: 0, height: -2)

// 动画配置
config.animationDuration = 0.25

// 背景遮罩
config.maskAlpha = 0.5
config.dismissOnBackgroundTap = true  // 默认为false，需要手动开启
```

### 背景点击关闭功能

BottomSheet组件支持通过点击背景遮罩来关闭弹窗，但这个功能**默认是关闭的**，需要手动开启：

```swift
var config = BottomSheetManager.Configuration()
config.dismissOnBackgroundTap = true  // 开启背景点击关闭

let manager = BottomSheetManager(configuration: config)
manager.show(customView: contentView, from: self)
```

#### 设计考虑

- **默认关闭**: 为了防止用户意外关闭重要的弹窗，该功能默认关闭
- **手动开启**: 只有在确实需要这个功能时才开启，比如信息展示类的弹窗
- **用户体验**: 开启后用户可以通过点击背景快速关闭弹窗，提升操作便利性

#### 适用场景

- ✅ **信息展示**: 纯展示类内容，用户查看后可快速关闭
- ✅ **选择器**: 用户可以通过点击背景取消选择
- ❌ **重要确认**: 需要用户明确选择的重要操作，不建议开启
- ❌ **表单填写**: 防止用户意外丢失已填写的内容

## 🎯 自定义内容视图

### 实现BottomSheetContentProtocol协议

```swift
class CustomSelectionView: UIView, BottomSheetContentProtocol {
    private var selectedIndex: Int?
    
    // MARK: - BottomSheetContentProtocol
    
    func getSelectedData() -> Any? {
        return selectedIndex
    }
    
    func validateSelection() -> Bool {
        return selectedIndex != nil
    }
    
    func resetSelection() {
        selectedIndex = nil
        // 更新UI状态
    }
}
```

## 📝 示例代码

### 不同HeaderView样式示例

```swift
// 标准样式 - 适用于需要确认/取消的操作
func showStandardStyle() {
    var config = BottomSheetManager.Configuration()
    config.headerStyle = .standard
    config.title = "确认操作"
    
    let manager = BottomSheetManager(configuration: config)
    manager.show(customView: contentView, from: self)
}

// 简洁样式 - 适用于信息展示
func showSimpleStyle() {
    var config = BottomSheetManager.Configuration()
    config.headerStyle = .simple
    config.title = "信息展示"
    
    let manager = BottomSheetManager(configuration: config)
    manager.show(customView: contentView, from: self)
}

// 标题+关闭样式 - 适用于详情查看
func showTitleOnlyStyle() {
    var config = BottomSheetManager.Configuration()
    config.headerStyle = .titleOnly
    config.title = "详情信息"
    
    let manager = BottomSheetManager(configuration: config)
    manager.show(customView: contentView, from: self)
}

// 纯标题样式 - 适用于纯展示
func showTitleWithoutButtonsStyle() {
    var config = BottomSheetManager.Configuration()
    config.headerStyle = .titleWithoutButtons
    config.title = "数据展示"
    
    let manager = BottomSheetManager(configuration: config)
    manager.show(customView: contentView, from: self)
}
```

## 🔧 扩展开发

### 添加新的HeaderView样式

1. 在`BottomSheetHeaderStyle`枚举中添加新的case
2. 创建新的HeaderView类，实现`BottomSheetHeaderViewProtocol`协议
3. 在枚举的`createHeaderView`方法中添加对应的创建逻辑

```swift
// 1. 添加新样式
public enum BottomSheetHeaderStyle {
    case custom  // 新增样式
    // ... 其他样式
    
    func createHeaderView(configuration: BottomSheetManager.Configuration) -> BottomSheetHeaderViewProtocol {
        switch self {
        case .custom:
            return CustomHeaderView(configuration: configuration)
        // ... 其他case
        }
    }
}

// 2. 实现新的HeaderView
class CustomHeaderView: UIView, BottomSheetHeaderViewProtocol {
    var headerHeight: CGFloat { return 70 }
    
    // 实现协议方法...
}
```

## 📁 文件结构

```
magicBox/CommonView/BottomSheet/
├── BottomSheetProtocols.swift      # 协议和枚举定义
├── BottomSheetManager.swift        # 主要管理器
├── BottomSheetHeaderViews.swift    # HeaderView实现
├── BottomSheetExample.swift        # 示例代码
└── BottomSheetUsage.md            # 使用说明文档
```

## 🐛 调试功能

在调试页面中提供了完整的测试功能：
- 不同HeaderView样式测试
- TabBar覆盖功能测试
- 各种配置选项测试

访问路径：设置页 → 调试页面 → HeaderView样式测试
