//
//  DatePickerView.swift
//  magicBox
//
//  Created by AI Assistant on 2025/7/16.
//

import UIKit
import SnapKit

// MARK: - 日期选择模式

public enum DateSelectionMode {
    case single    // 单选
    case multiple  // 多选
}

// MARK: - 日期选择器配置

public struct DatePickerConfiguration {
    /// 选择模式
    public var selectionMode: DateSelectionMode = .single
    /// 预设选中的日期
    public var preselectedDates: Set<Date> = []
    /// 最小可选日期
    public var minimumDate: Date?
    /// 最大可选日期
    public var maximumDate: Date?
    /// 初始显示的月份
    public var initialMonth: Date = Date()
    /// 是否显示今天按钮
    public var showTodayButton: Bool = true

    public init() {}
}

// MARK: - 基于UICalendarView的日期选择器

public class DatePickerView: UIView, BottomSheetContentProtocol {

    // MARK: - Properties

    private var configuration: DatePickerConfiguration
    private var selectedDates: Set<Date> = []

    // MARK: - UI Components

    private var headerView: UIView!
    private var todayButton: UIButton!
    private var calendarView: UICalendarView!

    private let calendar = Calendar.current

    // MARK: - Initialization

    public init(configuration: DatePickerConfiguration = DatePickerConfiguration()) {
        self.configuration = configuration
        self.selectedDates = configuration.preselectedDates

        super.init(frame: .zero)

        setupUI()
        configureCalendar()
        updateSelection()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Setup

    private func setupUI() {
        backgroundColor = AppTheme.Colors.background

        setupHeaderView()
        setupCalendarView()
        setupConstraints()
    }

    private func setupHeaderView() {
        headerView = UIView()
        headerView.backgroundColor = AppTheme.Colors.background
        addSubview(headerView)

        if configuration.showTodayButton {
            todayButton = UIButton(type: .system)
            todayButton.setTitle("今天", for: .normal)
            todayButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
            todayButton.setTitleColor(AppTheme.Colors.primary, for: .normal)
            todayButton.backgroundColor = AppTheme.Colors.secondaryBackground
            todayButton.layer.cornerRadius = 8
            todayButton.addTarget(self, action: #selector(todayButtonTapped), for: .touchUpInside)
            headerView.addSubview(todayButton)
        }
    }

    private func setupCalendarView() {
        if #available(iOS 16.0, *) {
            calendarView = UICalendarView()
            calendarView.delegate = self
            calendarView.calendar = calendar
            calendarView.locale = Locale(identifier: "zh_CN")
            calendarView.fontDesign = .default

            // 设置可见日期范围
            var dateInterval = DateInterval(start: Date(), end: calendar.date(byAdding: .year, value: 10, to: Date()) ?? Date())
            if let minDate = configuration.minimumDate {
                let maxDate = configuration.maximumDate ?? calendar.date(byAdding: .year, value: 10, to: Date()) ?? Date()
                dateInterval = DateInterval(start: minDate, end: maxDate)
            }
            calendarView.availableDateRange = dateInterval

            addSubview(calendarView)
        } else {
            // iOS 16以下版本的降级处理
            setupFallbackCalendarView()
        }
    }

    private func setupFallbackCalendarView() {
        let fallbackLabel = UILabel()
        fallbackLabel.text = "日期选择器需要iOS 16.0或更高版本"
        fallbackLabel.textAlignment = .center
        fallbackLabel.font = UIFont.systemFont(ofSize: 16)
        fallbackLabel.textColor = AppTheme.Colors.secondaryLabel
        fallbackLabel.numberOfLines = 0
        addSubview(fallbackLabel)

        fallbackLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(20)
        }
    }

    private func setupConstraints() {
        // 头部视图约束
        headerView.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(configuration.showTodayButton ? 60 : 0)
        }

        // 今天按钮约束
        if configuration.showTodayButton {
            todayButton.snp.makeConstraints { make in
                make.trailing.equalToSuperview().offset(-16)
                make.centerY.equalToSuperview()
                make.width.equalTo(60)
                make.height.equalTo(32)
            }
        }

        // 日历视图约束
        calendarView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom)
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(350) // 固定高度，确保显示完整
        }
    }

    // MARK: - Configuration

    private func configureCalendar() {
        // 设置选择行为
        switch configuration.selectionMode {
        case .single:
            let selection = UICalendarSelectionSingleDate(delegate: self)
            calendarView.selectionBehavior = selection
        case .multiple:
            let selection = UICalendarSelectionMultiDate(delegate: self)
            calendarView.selectionBehavior = selection
        }

        // 设置初始显示月份
        let visibleDateComponents = calendar.dateComponents([.year, .month], from: configuration.initialMonth)
        calendarView.visibleDateComponents = visibleDateComponents
    }

    private func updateSelection() {
        guard !selectedDates.isEmpty else { return }

        switch configuration.selectionMode {
        case .single:
            if let selection = calendarView.selectionBehavior as? UICalendarSelectionSingleDate,
               let firstDate = selectedDates.first {
                let dateComponents = calendar.dateComponents([.year, .month, .day], from: firstDate)
                selection.selectedDate = dateComponents
            }
        case .multiple:
            if let selection = calendarView.selectionBehavior as? UICalendarSelectionMultiDate {
                let dateComponentsArray = selectedDates.map { date in
                    calendar.dateComponents([.year, .month, .day], from: date)
                }
                selection.selectedDates = dateComponentsArray
            }
        }
    }

    // MARK: - Actions

    @objc private func todayButtonTapped() {
        let today = Date()
        let todayComponents = calendar.dateComponents([.year, .month], from: today)
        calendarView.visibleDateComponents = todayComponents

        // 如果是单选模式，自动选中今天
        if configuration.selectionMode == .single {
            let todayDateComponents = calendar.dateComponents([.year, .month, .day], from: today)
            if let selection = calendarView.selectionBehavior as? UICalendarSelectionSingleDate {
                selection.selectedDate = todayDateComponents
                selectedDates = [today]
            }
        }
    }

    // MARK: - BottomSheetContentProtocol

    public func getSelectedData() -> Any? {
        return Array(selectedDates).sorted()
    }

    public func validateSelection() -> Bool {
        return !selectedDates.isEmpty
    }

    public func resetSelection() {
        selectedDates.removeAll()

        switch configuration.selectionMode {
        case .single:
            if let selection = calendarView.selectionBehavior as? UICalendarSelectionSingleDate {
                selection.selectedDate = nil
            }
        case .multiple:
            if let selection = calendarView.selectionBehavior as? UICalendarSelectionMultiDate {
                selection.selectedDates = []
            }
        }
    }
}

// MARK: - UICalendarViewDelegate

@available(iOS 16.0, *)
extension DatePickerView: UICalendarViewDelegate {

    public func calendarView(_ calendarView: UICalendarView, decorationFor dateComponents: DateComponents) -> UICalendarView.Decoration? {
        // 可以在这里添加日期装饰，比如标记特殊日期
        return nil
    }
}

// MARK: - UICalendarSelectionSingleDateDelegate

@available(iOS 16.0, *)
extension DatePickerView: UICalendarSelectionSingleDateDelegate {

    public func dateSelection(_ selection: UICalendarSelectionSingleDate, didSelectDate dateComponents: DateComponents?) {
        if let dateComponents = dateComponents,
           let date = calendar.date(from: dateComponents) {
            selectedDates = [date]
        } else {
            selectedDates.removeAll()
        }
    }

    public func dateSelection(_ selection: UICalendarSelectionSingleDate, canSelectDate dateComponents: DateComponents?) -> Bool {
        guard let dateComponents = dateComponents,
              let date = calendar.date(from: dateComponents) else {
            return false
        }

        // 检查日期是否在允许范围内
        if let minimumDate = configuration.minimumDate,
           date < calendar.startOfDay(for: minimumDate) {
            return false
        }

        if let maximumDate = configuration.maximumDate,
           date > calendar.startOfDay(for: maximumDate) {
            return false
        }

        return true
    }
}

// MARK: - UICalendarSelectionMultiDateDelegate

@available(iOS 16.0, *)
extension DatePickerView: UICalendarSelectionMultiDateDelegate {

    public func multiDateSelection(_ selection: UICalendarSelectionMultiDate, didSelectDate dateComponents: DateComponents) {
        if let date = calendar.date(from: dateComponents) {
            selectedDates.insert(date)
        }
    }

    public func multiDateSelection(_ selection: UICalendarSelectionMultiDate, didDeselectDate dateComponents: DateComponents) {
        if let date = calendar.date(from: dateComponents) {
            selectedDates.remove(date)
        }
    }

    public func multiDateSelection(_ selection: UICalendarSelectionMultiDate, canSelectDate dateComponents: DateComponents) -> Bool {
        guard let date = calendar.date(from: dateComponents) else {
            return false
        }

        // 检查日期是否在允许范围内
        if let minimumDate = configuration.minimumDate,
           date < calendar.startOfDay(for: minimumDate) {
            return false
        }

        if let maximumDate = configuration.maximumDate,
           date > calendar.startOfDay(for: maximumDate) {
            return false
        }

        return true
    }

    public func multiDateSelection(_ selection: UICalendarSelectionMultiDate, canDeselectDate dateComponents: DateComponents) -> Bool {
        return true
    }
}


