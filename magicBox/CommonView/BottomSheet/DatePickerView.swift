//
//  DatePickerView.swift
//  magicBox
//
//  Created by AI Assistant on 2025/7/16.
//

import UIKit
import SnapKit

// MARK: - 日期选择模式

public enum DateSelectionMode {
    case single    // 单选
    case multiple  // 多选
}

// MARK: - 日期选择器配置

public struct DatePickerConfiguration {
    /// 选择模式
    public var selectionMode: DateSelectionMode = .single
    /// 预设选中的日期
    public var preselectedDates: Set<Date> = []
    /// 最小可选日期
    public var minimumDate: Date?
    /// 最大可选日期
    public var maximumDate: Date?
    /// 初始显示的月份
    public var initialMonth: Date = Date()
    /// 是否显示今天按钮
    public var showTodayButton: Bool = true

    public init() {}
}

// MARK: - 简化版日期选择器视图

public class DatePickerView: UIView, BottomSheetContentProtocol {

    // MARK: - Properties

    private var configuration: DatePickerConfiguration
    private var selectedDates: Set<Date> = []

    // MARK: - Initialization

    public init(configuration: DatePickerConfiguration = DatePickerConfiguration()) {
        self.configuration = configuration
        self.selectedDates = configuration.preselectedDates

        super.init(frame: .zero)

        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Setup

    private func setupUI() {
        backgroundColor = AppTheme.Colors.background

        let label = UILabel()
        label.text = "日期选择器 - 开发中"
        label.textAlignment = .center
        label.font = UIFont.systemFont(ofSize: 16)
        label.textColor = AppTheme.Colors.label
        addSubview(label)

        label.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.height.equalTo(200)
        }
    }

    // MARK: - BottomSheetContentProtocol

    public func getSelectedData() -> Any? {
        return Array(selectedDates).sorted()
    }

    public func validateSelection() -> Bool {
        return !selectedDates.isEmpty
    }

    public func resetSelection() {
        selectedDates.removeAll()
    }
}


