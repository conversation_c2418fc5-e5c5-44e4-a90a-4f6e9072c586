//
//  DatePickerView.swift
//  magicBox
//
//  Created by AI Assistant on 2025/7/16.
//

import UIKit
import SnapKit

// MARK: - 日期选择模式

public enum DateSelectionMode {
    case single    // 单选
    case multiple  // 多选
}

// MARK: - 日期选择器配置

public struct DatePickerConfiguration {
    /// 选择模式
    public var selectionMode: DateSelectionMode = .single
    /// 预设选中的日期
    public var preselectedDates: Set<Date> = []
    /// 最小可选日期
    public var minimumDate: Date?
    /// 最大可选日期
    public var maximumDate: Date?
    /// 初始显示的月份
    public var initialMonth: Date = Date()
    /// 是否显示今天按钮
    public var showTodayButton: Bool = true

    public init() {}
}

// MARK: - 日期选择器视图

public class DatePickerView: UIView, BottomSheetContentProtocol {
    
    // MARK: - Properties
    
    private var configuration: DatePickerConfiguration
    private var selectedDates: Set<Date> = []
    private var currentMonth: Date
    
    // MARK: - UI Components
    
    private var headerView: UIView!
    private var monthLabel: UILabel!
    private var previousButton: UIButton!
    private var nextButton: UIButton!
    private var todayButton: UIButton!
    
    private var weekdayHeaderView: UIView!
    private var calendarCollectionView: UICollectionView!
    
    private let calendar = Calendar.current
    private let dateFormatter = DateFormatter()
    
    // MARK: - Initialization
    
    public init(configuration: DatePickerConfiguration = DatePickerConfiguration()) {
        self.configuration = configuration
        self.currentMonth = configuration.initialMonth
        self.selectedDates = configuration.preselectedDates
        
        super.init(frame: .zero)
        
        setupDateFormatter()
        setupUI()
        updateMonthLabel()
        reloadCalendar()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    
    private func setupDateFormatter() {
        dateFormatter.locale = Locale(identifier: "zh_CN")
        dateFormatter.dateFormat = "yyyy年MM月"
    }
    
    private func setupUI() {
        backgroundColor = AppTheme.Colors.background
        
        setupHeaderView()
        setupWeekdayHeader()
        setupCalendarCollectionView()
        setupConstraints()
    }
    
    private func setupHeaderView() {
        headerView = UIView()
        headerView.backgroundColor = AppTheme.Colors.background
        addSubview(headerView)
        
        // 月份标签
        monthLabel = UILabel()
        monthLabel.font = UIFont.boldSystemFont(ofSize: 18)
        monthLabel.textColor = AppTheme.Colors.label
        monthLabel.textAlignment = .center
        headerView.addSubview(monthLabel)
        
        // 上一月按钮
        previousButton = UIButton(type: .system)
        previousButton.setImage(UIImage(systemName: "chevron.left"), for: .normal)
        previousButton.tintColor = AppTheme.Colors.primary
        previousButton.addTarget(self, action: #selector(previousMonthTapped), for: .touchUpInside)
        headerView.addSubview(previousButton)
        
        // 下一月按钮
        nextButton = UIButton(type: .system)
        nextButton.setImage(UIImage(systemName: "chevron.right"), for: .normal)
        nextButton.tintColor = AppTheme.Colors.primary
        nextButton.addTarget(self, action: #selector(nextMonthTapped), for: .touchUpInside)
        headerView.addSubview(nextButton)
        
        // 今天按钮
        if configuration.showTodayButton {
            todayButton = UIButton(type: .system)
            todayButton.setTitle("今天", for: .normal)
            todayButton.titleLabel?.font = UIFont.systemFont(ofSize: 14)
            todayButton.setTitleColor(AppTheme.Colors.primary, for: .normal)
            todayButton.addTarget(self, action: #selector(todayTapped), for: .touchUpInside)
            headerView.addSubview(todayButton)
        }
    }
    
    private func setupWeekdayHeader() {
        weekdayHeaderView = UIView()
        weekdayHeaderView.backgroundColor = AppTheme.Colors.secondarySystemBackground
        addSubview(weekdayHeaderView)
        
        let weekdays = ["日", "一", "二", "三", "四", "五", "六"]
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.alignment = .center
        weekdayHeaderView.addSubview(stackView)
        
        for weekday in weekdays {
            let label = UILabel()
            label.text = weekday
            label.textAlignment = .center
            label.font = UIFont.systemFont(ofSize: 14, weight: .medium)
            label.textColor = AppTheme.Colors.secondaryLabel
            stackView.addArrangedSubview(label)
        }
        
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(8)
        }
    }
    
    private func setupCalendarCollectionView() {
        let layout = UICollectionViewFlowLayout()
        layout.minimumLineSpacing = 0
        layout.minimumInteritemSpacing = 0
        
        calendarCollectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        calendarCollectionView.backgroundColor = AppTheme.Colors.background
        calendarCollectionView.delegate = self
        calendarCollectionView.dataSource = self
        calendarCollectionView.register(DateCell.self, forCellWithReuseIdentifier: "DateCell")
        calendarCollectionView.allowsMultipleSelection = (configuration.selectionMode == .multiple)
        
        addSubview(calendarCollectionView)
    }
    
    private func setupConstraints() {
        // 头部视图约束
        headerView.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(50)
        }
        
        // 月份标签约束
        monthLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        // 上一月按钮约束
        previousButton.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(44)
        }
        
        // 下一月按钮约束
        nextButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(44)
        }
        
        // 今天按钮约束
        if configuration.showTodayButton {
            todayButton.snp.makeConstraints { make in
                make.trailing.equalTo(nextButton.snp.leading).offset(-8)
                make.centerY.equalToSuperview()
                make.width.equalTo(40)
                make.height.equalTo(30)
            }
        }
        
        // 星期头部约束
        weekdayHeaderView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(40)
        }
        
        // 日历集合视图约束
        calendarCollectionView.snp.makeConstraints { make in
            make.top.equalTo(weekdayHeaderView.snp.bottom)
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(240) // 6行 * 40高度
        }
    }
    
    // MARK: - Actions
    
    @objc private func previousMonthTapped() {
        currentMonth = calendar.date(byAdding: .month, value: -1, to: currentMonth) ?? currentMonth
        updateMonthLabel()
        reloadCalendar()
    }
    
    @objc private func nextMonthTapped() {
        currentMonth = calendar.date(byAdding: .month, value: 1, to: currentMonth) ?? currentMonth
        updateMonthLabel()
        reloadCalendar()
    }
    
    @objc private func todayTapped() {
        let today = Date()
        currentMonth = today
        updateMonthLabel()
        reloadCalendar()
        
        // 如果是单选模式，自动选中今天
        if configuration.selectionMode == .single {
            selectedDates = [today]
            reloadCalendar()
        }
    }
    
    // MARK: - Helper Methods
    
    private func updateMonthLabel() {
        monthLabel.text = dateFormatter.string(from: currentMonth)
    }
    
    private func reloadCalendar() {
        calendarCollectionView.reloadData()
    }
    
    private func getDatesInMonth() -> [Date?] {
        guard let monthInterval = calendar.dateInterval(of: .month, for: currentMonth) else {
            return []
        }
        
        let firstOfMonth = monthInterval.start
        let firstWeekday = calendar.component(.weekday, from: firstOfMonth)
        let numberOfDaysInMonth = calendar.range(of: .day, in: .month, for: currentMonth)?.count ?? 0
        
        var dates: [Date?] = []
        
        // 添加前面的空白日期
        for _ in 1..<firstWeekday {
            dates.append(nil)
        }
        
        // 添加当月的日期
        for day in 1...numberOfDaysInMonth {
            if let date = calendar.date(byAdding: .day, value: day - 1, to: firstOfMonth) {
                dates.append(date)
            }
        }
        
        // 补齐到42个位置（6行 * 7列）
        while dates.count < 42 {
            dates.append(nil)
        }
        
        return dates
    }
    
    private func isDateSelected(_ date: Date) -> Bool {
        return selectedDates.contains { calendar.isDate($0, inSameDayAs: date) }
    }
    
    internal func isDateEnabled(_ date: Date) -> Bool {
        if let minimumDate = configuration.minimumDate,
           date < calendar.startOfDay(for: minimumDate) {
            return false
        }
        
        if let maximumDate = configuration.maximumDate,
           date > calendar.startOfDay(for: maximumDate) {
            return false
        }
        
        return true
    }
    
    // MARK: - BottomSheetContentProtocol
    
    public func getSelectedData() -> Any? {
        return Array(selectedDates).sorted()
    }
    
    public func validateSelection() -> Bool {
        return !selectedDates.isEmpty
    }
    
    public func resetSelection() {
        selectedDates.removeAll()
        reloadCalendar()
    }
}

// MARK: - UICollectionViewDataSource & UICollectionViewDelegate

extension DatePickerView: UICollectionViewDataSource, UICollectionViewDelegate, UICollectionViewDelegateFlowLayout {

    public func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return 42 // 6行 * 7列
    }

    public func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "DateCell", for: indexPath) as! DateCell

        let dates = getDatesInMonth()
        let date = dates[indexPath.item]

        if let date = date {
            let day = calendar.component(.day, from: date)
            let isSelected = isDateSelected(date)
            let isEnabled = isDateEnabled(date)
            let isToday = calendar.isDateInToday(date)
            let isCurrentMonth = calendar.isDate(date, equalTo: currentMonth, toGranularity: .month)

            cell.configure(
                day: day,
                isSelected: isSelected,
                isEnabled: isEnabled && isCurrentMonth,
                isToday: isToday,
                isCurrentMonth: isCurrentMonth
            )
        } else {
            cell.configure(day: nil, isSelected: false, isEnabled: false, isToday: false, isCurrentMonth: false)
        }

        return cell
    }

    public func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let dates = getDatesInMonth()
        guard let date = dates[indexPath.item],
              isDateEnabled(date),
              calendar.isDate(date, equalTo: currentMonth, toGranularity: .month) else {
            return
        }

        let startOfDay = calendar.startOfDay(for: date)

        switch configuration.selectionMode {
        case .single:
            selectedDates = [startOfDay]
        case .multiple:
            if selectedDates.contains(where: { calendar.isDate($0, inSameDayAs: startOfDay) }) {
                selectedDates.removeAll { calendar.isDate($0, inSameDayAs: startOfDay) }
            } else {
                selectedDates.insert(startOfDay)
            }
        }

        reloadCalendar()
    }

    public func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let width = collectionView.bounds.width / 7
        return CGSize(width: width, height: 40)
    }
}

// MARK: - DateCell

private class DateCell: UICollectionViewCell {

    private let dayLabel = UILabel()
    private let selectionIndicator = UIView()
    private let todayIndicator = UIView()

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupCell()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupCell() {
        // 选中指示器
        selectionIndicator.backgroundColor = AppTheme.Colors.primary
        selectionIndicator.layer.cornerRadius = 18
        selectionIndicator.isHidden = true
        contentView.addSubview(selectionIndicator)

        // 今天指示器（小点）
        todayIndicator.backgroundColor = AppTheme.Colors.primary
        todayIndicator.layer.cornerRadius = 2
        todayIndicator.isHidden = true
        contentView.addSubview(todayIndicator)

        // 日期标签
        dayLabel.textAlignment = .center
        dayLabel.font = UIFont.systemFont(ofSize: 16)
        contentView.addSubview(dayLabel)

        // 约束
        selectionIndicator.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(36)
        }

        dayLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }

        todayIndicator.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().offset(-6)
            make.width.height.equalTo(4)
        }
    }

    func configure(day: Int?, isSelected: Bool, isEnabled: Bool, isToday: Bool, isCurrentMonth: Bool) {
        if let day = day {
            dayLabel.text = "\(day)"
            dayLabel.isHidden = false

            // 设置选中状态
            selectionIndicator.isHidden = !isSelected

            // 设置今天指示器
            todayIndicator.isHidden = !isToday || isSelected

            // 设置文字颜色
            if isSelected {
                dayLabel.textColor = .white
            } else if !isEnabled {
                dayLabel.textColor = AppTheme.Colors.tertiaryLabel
            } else if !isCurrentMonth {
                dayLabel.textColor = AppTheme.Colors.tertiaryLabel
            } else {
                dayLabel.textColor = AppTheme.Colors.label
            }

            // 设置字体权重
            dayLabel.font = isToday ? UIFont.boldSystemFont(ofSize: 16) : UIFont.systemFont(ofSize: 16)

        } else {
            dayLabel.isHidden = true
            selectionIndicator.isHidden = true
            todayIndicator.isHidden = true
        }
    }
}
